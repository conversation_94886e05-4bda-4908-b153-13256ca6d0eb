'use client';

import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import TechnologyHorizonTimeline from './TechnologyHorizonTimeline';
import DigitalTwinSpotlight from './DigitalTwinSpotlight';
import ReadinessAssessment from './ReadinessAssessment';
import useIntersectionObserver from '@/hooks/useIntersectionObserver';

interface FuturesPageContentProps {
  translations?: {
    title?: string;
    subtitle?: string;
    heroTitle?: string;
    heroSubtitle?: string;
    exploreButton?: string;
    introduction?: {
      headline?: string;
      description?: string;
    };
    technologyHorizon?: {
      title?: string;
      nearHorizon?: {
        title?: string;
        technologies?: {
          edgeAI?: {
            title?: string;
            description?: string;
          };
          hybridQuantum?: {
            title?: string;
            description?: string;
          };
          zeroTrust?: {
            title?: string;
            description?: string;
          };
        };
      };
      midHorizon?: {
        title?: string;
        technologies?: {
          neuromorphic?: {
            title?: string;
            description?: string;
          };
          digitalTwin?: {
            title?: string;
            description?: string;
          };
          ambientIntelligence?: {
            title?: string;
            description?: string;
          };
        };
      };
      farHorizon?: {
        title?: string;
        technologies?: {
          autonomousEnterprise?: {
            title?: string;
            description?: string;
          };
          syntheticData?: {
            title?: string;
            description?: string;
          };
          biologicalComputing?: {
            title?: string;
            description?: string;
          };
        };
      };
    };
    digitalTwin?: {
      title?: string;
      overview?: {
        title?: string;
        description?: string;
      };
      components?: {
        title?: string;
        realityCaptureEngine?: {
          title?: string;
          features?: string[];
        };
        simulationHypervisor?: {
          title?: string;
          features?: string[];
        };
        decisionAugmentation?: {
          title?: string;
          features?: string[];
        };
      };
      implementationPathway?: {
        title?: string;
        steps?: string[];
      };
      caseStudy?: {
        title?: string;
        headline?: string;
        metrics?: string[];
      };
    };
    readinessAssessment?: {
      title?: string;
      description?: string;
      areas?: {
        infrastructure?: string;
        dataArchitecture?: string;
        workforce?: string;
        governance?: string;
        innovation?: string;
      };
      interactivePrompt?: string;
    };
  };
}

export default function FuturesPageContent({ translations = {} }: FuturesPageContentProps) {
  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 0.1 });
  const sectionRef = useRef<HTMLDivElement>(null);

  return (
    <div className="relative min-h-screen bg-space-black pt-32 pb-20" ref={sectionRef}>
      {/* Hero Section */}
      <section className="relative mb-24">
        <div className="absolute inset-0 z-0 opacity-20">
          <div className="absolute inset-0 bg-gradient-to-b from-purple-900/30 to-transparent"></div>
          <div className="h-full w-full bg-[url('/images/future-tech-grid.svg')] bg-cover bg-center"></div>
        </div>
        
        <div className="container relative z-10 mx-auto px-4">
          <motion.h1 
            className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-center bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-purple-500 to-indigo-600"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {translations.heroTitle}
          </motion.h1>
          
          <motion.p 
            className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {translations.heroSubtitle}
          </motion.p>
          
          <motion.div 
            className="flex justify-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <button className="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full text-white font-medium hover:from-blue-700 hover:to-purple-700 transition-all shadow-lg hover:shadow-purple-500/20">
              {translations.exploreButton}
            </button>
          </motion.div>
        </div>
      </section>
      
      {/* Introduction Panel */}
      <section className="mb-24" ref={ref}>
        <div className="container mx-auto px-4">
          <motion.div 
            className="bg-deep-space border border-purple-500/20 rounded-xl p-8 md:p-12 max-w-5xl mx-auto"
            initial={{ opacity: 0, y: 40 }}
            animate={isIntersecting ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-2xl md:text-3xl font-bold mb-6 text-white">
              {translations.introduction?.headline}
            </h2>
            <p className="text-gray-300 text-lg leading-relaxed">
              {translations.introduction?.description}
            </p>
          </motion.div>
        </div>
      </section>
      
      {/* Technology Horizon Timeline */}
      <TechnologyHorizonTimeline translations={translations.technologyHorizon} />
      
      {/* Digital Twin Spotlight */}
      <DigitalTwinSpotlight translations={translations.digitalTwin} />
      
      {/* Readiness Assessment */}
      <ReadinessAssessment translations={translations.readinessAssessment} />
    </div>
  );
}
