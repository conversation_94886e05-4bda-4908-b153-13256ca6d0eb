import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

// Define the request body type
interface EmailRequestBody {
  name: string;
  email: string;
  service: string;
  message: string;
  subject?: string;
}

// Use named export instead of default export
export async function POST(request: NextRequest) {
  try {
    const { name, email, service, message, subject } = await request.json() as EmailRequestBody;

    // Validate required fields
    if (!name || !email || !service || !message) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Configure email transporter
    // Note: For production, use your actual SMTP credentials
    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: parseInt(process.env.EMAIL_PORT || '587'),
      secure: process.env.EMAIL_SECURE === 'true',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD,
      },
    });

    // Customize the email content
    const mailOptions = {
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: 'd<PERSON><PERSON><PERSON><PERSON>@elysian-systems.com',
      replyTo: email,
      subject: subject || `New website inquiry: ${service}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">New Contact Form Submission</h2>
          <p style="color: #666;">You've received a new inquiry from your website contact form.</p>

          <div style="background-color: #f7f7f7; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <p><strong>Name:</strong> ${name}</p>
            <p><strong>Email:</strong> ${email}</p>
            <p><strong>Service:</strong> ${service}</p>
            <p><strong>Message:</strong></p>
            <p style="white-space: pre-wrap;">${message}</p>
          </div>

          <p style="color: #888; font-size: 12px;">This email was sent from your website contact form.</p>
        </div>
      `,
    };

    // Send the email
    await transporter.sendMail(mailOptions);

    return NextResponse.json(
      { success: true, message: 'Email sent successfully' },
      { status: 200 }
    );
  } catch (error: any) {
    console.error('Email sending error:', error);

    return NextResponse.json(
      { error: 'Failed to send email', details: error.message },
      { status: 500 }
    );
  }
}
