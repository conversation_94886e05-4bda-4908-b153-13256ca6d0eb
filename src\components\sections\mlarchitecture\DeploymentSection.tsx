'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import useIntersectionObserver from '@/hooks/useIntersectionObserver';

interface DeploymentSectionProps {
  translations: {
    title?: string;
    description?: string;
    icon?: string;
    components?: string[];
    noDescriptionAvailable: string;
  };
  componentNames?: Record<string, string>;
  componentDetails?: Record<string, { description?: string } | string>;
}

export default function DeploymentSection({
  translations,
  componentNames,
  componentDetails
}: DeploymentSectionProps) {
  const [selectedComponent, setSelectedComponent] = useState<string | null>(null);
  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 0.1 });

  // Debug logging
  useEffect(() => {
    console.log('DeploymentSection - componentDetails:', componentDetails);
    console.log('DeploymentSection - componentNames:', componentNames);
    console.log('DeploymentSection - translations:', translations);
  }, [componentDetails, componentNames, translations]);

  // Function to handle component click
  const handleComponentClick = (componentName: string, componentKey?: string) => {
    setSelectedComponent(componentKey || componentName);
  };

  // Function to close component details
  const handleCloseDetails = () => {
    setSelectedComponent(null);
  };

  // Helper function to get component array
  const getComponentsArray = (components?: string[] | string): string[] => {
    if (!components) return [];

    if (Array.isArray(components)) {
      return components;
    } else {
      try {
        // Try to parse as JSON
        return JSON.parse(components);
      } catch {
        // If parsing fails, it might be a comma-separated string
        return components.split(',').map(item => item.trim());
      }
    }
  };

  // Get components array
  const componentsArray = getComponentsArray(translations?.components);

  // Animation variants
  const phaseVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5,
      },
    }),
  };

  // Helper function to get translated component name
  const getTranslatedComponentName = (componentKey: string): string => {
    return componentNames && componentNames[componentKey]
      ? componentNames[componentKey]
      : componentKey;
  };

  // Helper to get the description for a component
  const getComponentDescription = (key: string | null | undefined): string => {
    if (!key) return translations.noDescriptionAvailable;
    const detail = componentDetails?.[key];
    if (detail) {
      if (typeof detail === 'object' && detail.description) return detail.description;
      if (typeof detail === 'string') return detail;
    }
    return translations.noDescriptionAvailable;
  };

  return (
    <div ref={ref} className="w-full">
      <motion.div
        className="bg-gray-900/40 backdrop-blur-sm rounded-xl shadow-lg border border-purple-700/30 p-8 md:p-12 w-full mx-auto"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="flex flex-col md:flex-row items-start gap-6">
          <div className="text-cyan-400 text-3xl flex-shrink-0">
            {translations?.icon}
          </div>

          <div className="flex-grow">
            <h3 className="text-2xl font-bold mb-4 text-white">
              {translations?.title}
            </h3>
            <p className="text-lg text-gray-300 mb-6">
              {translations?.description}
            </p>

            {/* Components Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {componentsArray.map((componentKey, idx) => {
                // Get the translated component name
                const componentDisplayName = getTranslatedComponentName(componentKey);

                return (
                  <motion.div
                    key={componentKey}
                    className={`component-card bg-gray-800/50 border ${
                      selectedComponent === componentKey
                        ? 'border-cyan-500 ring-1 ring-cyan-500/50'
                        : 'border-purple-500/20'
                    } rounded-lg p-4 cursor-pointer transition-all hover:bg-gray-800/80 hover:border-cyan-500/50 hover:shadow-lg hover:shadow-cyan-500/10 relative group`}
                    custom={idx}
                    variants={phaseVariants}
                    initial="hidden"
                    animate={isIntersecting ? "visible" : "hidden"}
                    onClick={() => handleComponentClick(componentDisplayName, componentKey)}
                    whileHover={{ scale: 1.02 }}
                  >
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-white font-medium">{componentDisplayName}</span>
                      <span className="text-cyan-400 opacity-0 group-hover:opacity-100 transition-opacity flex items-center">
                        <span className="text-xs mr-1">View details</span>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </span>
                    </div>
                    <div className="text-gray-300 text-sm mt-2 h-12 overflow-hidden">
                      {getComponentDescription(componentKey)}
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Component Details Modal */}
      {selectedComponent && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className="bg-gray-900 border border-purple-500/30 rounded-xl p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
          >
            <div className="relative">
              <button
                onClick={handleCloseDetails}
                className="absolute top-0 right-0 text-gray-400 hover:text-white"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
              <div className="flex flex-col sm:flex-row items-center sm:items-start gap-4">
                <div className="text-3xl bg-gradient-to-br from-cyan-400 to-purple-500 rounded-full h-14 w-14 flex items-center justify-center flex-shrink-0">
                  <span>🔍</span>
                </div>
                <div className="text-center sm:text-left">
                  <h3 className="text-xl font-semibold mb-3 text-white">
                    {selectedComponent && getTranslatedComponentName(selectedComponent)}
                  </h3>
                  <p className="text-gray-300">
                    {getComponentDescription(selectedComponent)}
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </div>
  );
}
