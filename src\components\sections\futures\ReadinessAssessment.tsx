'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import useIntersectionObserver from '@/hooks/useIntersectionObserver';

interface ReadinessAssessmentProps {
  translations?: {
    title?: string;
    description?: string;
    areas?: {
      infrastructure?: string;
      dataArchitecture?: string;
      workforce?: string;
      governance?: string;
      innovation?: string;
    };
    interactivePrompt?: string;
  };
}

export default function ReadinessAssessment({ translations = {} }: ReadinessAssessmentProps) {
  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 0.1 });
  const [assessmentValues, setAssessmentValues] = useState({
    infrastructure: 3,
    dataArchitecture: 2,
    workforce: 3,
    governance: 2,
    innovation: 4
  });

  // Calculate the points for the radar chart
  const calculateRadarPoints = () => {
    const areas = ['infrastructure', 'dataArchitecture', 'workforce', 'governance', 'innovation'];
    const numPoints = areas.length;
    const angleStep = (Math.PI * 2) / numPoints;
    const radius = 100; // Base radius for the chart
    
    return areas.map((area, i) => {
      const value = assessmentValues[area as keyof typeof assessmentValues];
      const scaledRadius = (value / 5) * radius; // Scale based on value (1-5)
      const angle = i * angleStep - Math.PI / 2; // Start from top (- PI/2)
      const x = scaledRadius * Math.cos(angle) + radius;
      const y = scaledRadius * Math.sin(angle) + radius;
      return { x, y, area, value };
    });
  };

  const radarPoints = calculateRadarPoints();
  const polygonPoints = radarPoints.map(point => `${point.x},${point.y}`).join(' ');
  
  // Generate recommendations based on assessment
  const getRecommendations = () => {
    const weakestArea = Object.entries(assessmentValues).reduce(
      (min, [area, value]) => (value < min.value ? { area, value } : min),
      { area: '', value: 5 }
    );
    
    const recommendations = {
      infrastructure: "Focus on modernizing your infrastructure with containerization and microservices to increase flexibility.",
      dataArchitecture: "Implement a data mesh architecture to improve data accessibility and governance across your organization.",
      workforce: "Invest in upskilling programs focused on emerging technologies and agile methodologies.",
      governance: "Develop adaptive governance frameworks that balance innovation with compliance requirements.",
      innovation: "Establish innovation labs and allocate dedicated resources for exploring emerging technologies."
    };
    
    return recommendations[weakestArea.area as keyof typeof recommendations];
  };

  const handleSliderChange = (area: string, value: number) => {
    setAssessmentValues(prev => ({
      ...prev,
      [area]: value
    }));
  };

  return (
    <section className="mb-24" ref={ref}>
      <div className="container mx-auto px-4">
        <motion.h2
          className="text-3xl md:text-4xl font-bold mb-6 text-center text-white"
          initial={{ opacity: 0, y: 20 }}
          animate={isIntersecting ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6 }}
        >
          {translations.title}
        </motion.h2>
        
        <motion.p
          className="text-lg text-gray-300 max-w-3xl mx-auto text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={isIntersecting ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {translations.description}
        </motion.p>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Radar Chart */}
          <motion.div
            className="bg-deep-space border border-purple-500/20 rounded-xl p-6 flex items-center justify-center"
            initial={{ opacity: 0, x: -40 }}
            animate={isIntersecting ? { opacity: 1, x: 0 } : { opacity: 0, x: -40 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <div className="relative w-[250px] h-[250px]">
              <svg width="250" height="250" viewBox="0 0 200 200">
                {/* Background circles */}
                {[1, 2, 3, 4, 5].map((level) => (
                  <circle
                    key={level}
                    cx="100"
                    cy="100"
                    r={level * 20}
                    fill="none"
                    stroke="#4B5563"
                    strokeWidth="0.5"
                    strokeDasharray="2,2"
                  />
                ))}
                
                {/* Axis lines */}
                {radarPoints.map((point, i) => (
                  <line
                    key={i}
                    x1="100"
                    y1="100"
                    x2={100 + (point.x - 100) * 5 / point.value}
                    y2={100 + (point.y - 100) * 5 / point.value}
                    stroke="#6B7280"
                    strokeWidth="0.5"
                  />
                ))}
                
                {/* Data polygon */}
                <polygon
                  points={polygonPoints}
                  fill="rgba(139, 92, 246, 0.2)"
                  stroke="rgba(139, 92, 246, 0.8)"
                  strokeWidth="2"
                />
                
                {/* Data points */}
                {radarPoints.map((point, i) => (
                  <circle
                    key={i}
                    cx={point.x}
                    cy={point.y}
                    r="4"
                    fill="#8B5CF6"
                  />
                ))}
                
                {/* Labels */}
                {radarPoints.map((point, i) => {
                  const angle = i * (Math.PI * 2) / 5 - Math.PI / 2;
                  const labelRadius = 120;
                  const labelX = labelRadius * Math.cos(angle) + 100;
                  const labelY = labelRadius * Math.sin(angle) + 100;
                  
                  // Adjust text anchor based on position
                  let textAnchor = "middle";
                  if (labelX < 90) textAnchor = "end";
                  if (labelX > 110) textAnchor = "start";
                  
                  return (
                    <text
                      key={i}
                      x={labelX}
                      y={labelY}
                      fill="white"
                      fontSize="8"
                      textAnchor={textAnchor}
                      dominantBaseline="middle"
                    >
                      {translations.areas?.[point.area as keyof typeof translations.areas]}
                    </text>
                  );
                })}
              </svg>
            </div>
          </motion.div>
          
          {/* Sliders and Recommendations */}
          <motion.div
            className="flex flex-col"
            initial={{ opacity: 0, x: 40 }}
            animate={isIntersecting ? { opacity: 1, x: 0 } : { opacity: 0, x: 40 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <p className="text-gray-300 mb-6">{translations.interactivePrompt}</p>
            
            {/* Sliders */}
            <div className="space-y-6 mb-8">
              {Object.entries(translations.areas || {}).map(([key, label]) => (
                <div key={key} className="space-y-2">
                  <div className="flex justify-between">
                    <label className="text-gray-300 text-sm">{label}</label>
                    <span className="text-purple-400 font-medium">
                      {assessmentValues[key as keyof typeof assessmentValues]}/5
                    </span>
                  </div>
                  <input
                    type="range"
                    min="1"
                    max="5"
                    step="1"
                    value={assessmentValues[key as keyof typeof assessmentValues]}
                    onChange={(e) => handleSliderChange(key, parseInt(e.target.value))}
                    className="w-full accent-purple-500 bg-deep-space h-2 rounded-lg appearance-none cursor-pointer"
                  />
                </div>
              ))}
            </div>
            
            {/* Recommendations */}
            <div className="bg-deep-space border border-purple-500/20 rounded-xl p-6">
              <h3 className="text-xl font-bold mb-4 text-white">Personalized Recommendation</h3>
              <p className="text-gray-300">{getRecommendations()}</p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
