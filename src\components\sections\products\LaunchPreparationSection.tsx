'use client';

import { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import useIntersectionObserver from '@/hooks/useIntersectionObserver';
import ProcessDetails from '@/components/sections/products/ProcessDetails';

interface LaunchPreparationSectionProps {
  translations?: {
    managementPhases?: {
      launchPreparation?: {
        title?: string;
        description?: string;
        processes?: {
          marketingStrategy?: string;
          launchPreparation?: string;
          communicationPlan?: string;
          organizationalChart?: string;
          changeManagement?: string;
        };
      };
    };
    components?: {
      keyProcesses?: string;
      processDetailsContent?: {
        [key: string]: {
          description?: string;
          features?: string[];
        };
      };
    };
  };
}

export default function LaunchPreparationSection({ translations }: LaunchPreparationSectionProps) {
  const [selectedProcess, setSelectedProcess] = useState<string | null>(null);
  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 0.3 });
  const processDetailsRef = useRef<HTMLDivElement>(null);

  const handleProcessClick = (process: string) => {
    setSelectedProcess(process === selectedProcess ? null : process);
    
    // Scroll to process details when a process is selected
    if (process !== selectedProcess && processDetailsRef.current) {
      setTimeout(() => {
        processDetailsRef.current?.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      }, 100);
    }
  };

  const processes = [
    translations?.managementPhases?.launchPreparation?.processes?.marketingStrategy || 'Marketing Strategy',
    translations?.managementPhases?.launchPreparation?.processes?.launchPreparation || 'Launch Preparation',
    translations?.managementPhases?.launchPreparation?.processes?.communicationPlan || 'Communication Plan',
    translations?.managementPhases?.launchPreparation?.processes?.organizationalChart || 'Organizational Chart',
    translations?.managementPhases?.launchPreparation?.processes?.changeManagement || 'Change Management'
  ];

  const phaseVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5
      }
    })
  };

  return (
    <div ref={ref} className="relative">
      <motion.div
        className="bg-gray-900/40 backdrop-blur-sm rounded-xl shadow-lg border border-purple-700/30 p-8 md:p-12 max-w-5xl mx-auto"
        initial={{ opacity: 0, y: 20 }}
        animate={isIntersecting ? { opacity: 1, y: 0 } : {}}
        transition={{ duration: 0.6 }}
      >
        <div className="flex flex-col md:flex-row items-start gap-8">
          <div className="text-cyan-400 flex-shrink-0">
            <svg className="w-12 h-12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
              <path strokeLinecap="round" strokeLinejoin="round" d="M15.59 14.37a6 6 0 01-5.84 7.38v-4.8m5.84-2.58a14.98 14.98 0 006.16-12.12A14.98 14.98 0 009.631 8.41m5.96 5.96a14.926 14.926 0 01-5.841 2.58m-.119-8.54a6 6 0 00-7.381 5.84h4.8m2.581-5.84a14.927 14.927 0 00-2.58 5.84m2.699 2.7c-.103.021-.207.041-.311.06a15.09 15.09 0 01-2.448-2.448 14.9 14.9 0 01.06-.312m-2.24 2.39a4.493 4.493 0 00-1.757 4.306 4.493 4.493 0 004.306-1.758M16.5 9a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
            </svg>
          </div>

          <div className="flex-grow">
            <h2 className="text-3xl font-bold mb-4 text-white">
              {translations?.managementPhases?.launchPreparation?.title || 'Launch Preparation & Execution'}
            </h2>
            <p className="text-lg text-gray-300 mb-8">
              {translations?.managementPhases?.launchPreparation?.description || 'Prepare for successful product launches with comprehensive planning, marketing strategies, and organizational alignment. Ensure all stakeholders are prepared for change and communication plans are in place for maximum market impact.'}
            </p>

            <h3 className="text-xl font-semibold mb-4 text-cyan-400">
              {translations?.components?.keyProcesses || 'Key Processes'}
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {processes.map((process, idx) => (
                <motion.div
                  key={process}
                  className={`bg-gray-800/50 border ${
                    selectedProcess === process
                      ? 'border-cyan-500 ring-1 ring-cyan-500/50'
                      : 'border-purple-500/20'
                  } rounded-lg p-4 cursor-pointer transition-all hover:bg-gray-800/80 hover:border-cyan-500/50 hover:shadow-lg hover:shadow-cyan-500/10 relative group`}
                  custom={idx}
                  variants={phaseVariants}
                  initial="hidden"
                  animate={isIntersecting ? "visible" : "hidden"}
                  onClick={() => handleProcessClick(process)}
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="flex justify-between items-center">
                    <span className="text-white font-medium">{process}</span>
                    <span className="text-cyan-400 opacity-0 group-hover:opacity-100 transition-opacity flex items-center">
                      <span className="text-xs mr-1">View details</span>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </span>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Process Details Section */}
            {selectedProcess && (
              <div ref={processDetailsRef}>
                <ProcessDetails 
                  process={selectedProcess} 
                  translations={translations} 
                  onClose={() => setSelectedProcess(null)} 
                />
              </div>
            )}
          </div>
        </div>
      </motion.div>
    </div>
  );
}
