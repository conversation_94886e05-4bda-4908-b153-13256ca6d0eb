'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import useIntersectionObserver from '@/hooks/useIntersectionObserver';

interface DigitalTwinSpotlightProps {
  translations?: {
    title?: string;
    overview?: {
      title?: string;
      description?: string;
    };
    components?: {
      title?: string;
      realityCaptureEngine?: {
        title?: string;
        features?: string[];
      };
      simulationHypervisor?: {
        title?: string;
        features?: string[];
      };
      decisionAugmentation?: {
        title?: string;
        features?: string[];
      };
    };
    implementationPathway?: {
      title?: string;
      steps?: string[];
    };
    caseStudy?: {
      title?: string;
      headline?: string;
      metrics?: string[];
    };
  };
}

export default function DigitalTwinSpotlight({ translations = {} }: DigitalTwinSpotlightProps) {
  const [activeComponent, setActiveComponent] = useState<string>('realityCaptureEngine');
  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 0.1 });

  // Component icons
  const componentIcons = {
    realityCaptureEngine: '📡',
    simulationHypervisor: '🔄',
    decisionAugmentation: '🧠'
  };

  return (
    <section className="mb-24 py-16 bg-gradient-to-b from-deep-space to-space-black" ref={ref}>
      <div className="container mx-auto px-4">
        <motion.h2
          className="text-3xl md:text-4xl font-bold mb-6 text-center text-white"
          initial={{ opacity: 0, y: 20 }}
          animate={isIntersecting ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6 }}
        >
          {translations.title}
        </motion.h2>

        {/* System Overview */}
        <motion.div
          className="bg-deep-space border border-purple-500/20 rounded-xl p-8 max-w-4xl mx-auto mb-16"
          initial={{ opacity: 0, y: 40 }}
          animate={isIntersecting ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
          transition={{ duration: 0.8 }}
        >
          <h3 className="text-2xl font-bold mb-4 text-white">{translations.overview?.title}</h3>
          <p className="text-gray-300 text-lg">{translations.overview?.description}</p>
        </motion.div>

        {/* 3D Representation and Components */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* 3D Visualization */}
          <motion.div
            className="relative h-[400px] bg-deep-space rounded-xl overflow-hidden border border-purple-500/20"
            initial={{ opacity: 0, x: -40 }}
            animate={isIntersecting ? { opacity: 1, x: 0 } : { opacity: 0, x: -40 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10"></div>
            
            {/* 3D Model Placeholder */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="relative w-64 h-64">
                {/* Central Node */}
                <motion.div 
                  className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold z-10"
                  animate={{ 
                    scale: [1, 1.05, 1],
                    boxShadow: [
                      '0 0 0 rgba(139, 92, 246, 0.4)',
                      '0 0 20px rgba(139, 92, 246, 0.6)',
                      '0 0 0 rgba(139, 92, 246, 0.4)'
                    ]
                  }}
                  transition={{ 
                    duration: 3,
                    repeat: Infinity,
                    repeatType: 'loop'
                  }}
                >
                  Digital<br/>Twin
                </motion.div>
                
                {/* Orbiting Nodes */}
                {['Reality', 'Simulation', 'Decision'].map((label, i) => {
                  const angle = (2 * Math.PI * i) / 3;
                  const x = Math.cos(angle) * 100;
                  const y = Math.sin(angle) * 100;
                  
                  return (
                    <motion.div
                      key={label}
                      className="absolute w-16 h-16 bg-deep-space border border-purple-500 rounded-full flex items-center justify-center text-white text-sm"
                      style={{
                        left: `calc(50% + ${x}px - 32px)`,
                        top: `calc(50% + ${y}px - 32px)`,
                      }}
                      animate={{
                        scale: [1, 1.1, 1],
                        borderColor: [
                          'rgba(139, 92, 246, 0.5)',
                          'rgba(139, 92, 246, 1)',
                          'rgba(139, 92, 246, 0.5)'
                        ]
                      }}
                      transition={{
                        duration: 3,
                        delay: i * 0.5,
                        repeat: Infinity,
                        repeatType: 'loop'
                      }}
                    >
                      {label}
                    </motion.div>
                  );
                })}
                
                {/* Connection Lines */}
                <svg className="absolute inset-0 w-full h-full" style={{ zIndex: 0 }}>
                  {['Reality', 'Simulation', 'Decision'].map((_, i) => {
                    const angle = (2 * Math.PI * i) / 3;
                    const x = Math.cos(angle) * 100;
                    const y = Math.sin(angle) * 100;
                    
                    return (
                      <motion.line
                        key={i}
                        x1="50%"
                        y1="50%"
                        x2={`calc(50% + ${x}px)`}
                        y2={`calc(50% + ${y}px)`}
                        stroke="url(#digitalTwinGradient)"
                        strokeWidth="2"
                        strokeDasharray="5,5"
                        animate={{
                          strokeDashoffset: [0, 100]
                        }}
                        transition={{
                          duration: 20,
                          repeat: Infinity,
                          repeatType: 'loop',
                          ease: "linear"
                        }}
                      />
                    );
                  })}
                  <defs>
                    <linearGradient id="digitalTwinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#3B82F6" />
                      <stop offset="100%" stopColor="#8B5CF6" />
                    </linearGradient>
                  </defs>
                </svg>
              </div>
            </div>
          </motion.div>
          
          {/* Components */}
          <motion.div
            className="flex flex-col"
            initial={{ opacity: 0, x: 40 }}
            animate={isIntersecting ? { opacity: 1, x: 0 } : { opacity: 0, x: 40 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <h3 className="text-2xl font-bold mb-6 text-white">{translations.components?.title}</h3>
            
            {/* Component Tabs */}
            <div className="flex mb-6 border-b border-gray-700">
              <button
                className={`px-4 py-2 font-medium ${
                  activeComponent === 'realityCaptureEngine'
                    ? 'text-blue-400 border-b-2 border-blue-400'
                    : 'text-gray-400 hover:text-white'
                }`}
                onClick={() => setActiveComponent('realityCaptureEngine')}
              >
                {componentIcons.realityCaptureEngine} {translations.components?.realityCaptureEngine?.title}
              </button>
              <button
                className={`px-4 py-2 font-medium ${
                  activeComponent === 'simulationHypervisor'
                    ? 'text-purple-400 border-b-2 border-purple-400'
                    : 'text-gray-400 hover:text-white'
                }`}
                onClick={() => setActiveComponent('simulationHypervisor')}
              >
                {componentIcons.simulationHypervisor} {translations.components?.simulationHypervisor?.title}
              </button>
              <button
                className={`px-4 py-2 font-medium ${
                  activeComponent === 'decisionAugmentation'
                    ? 'text-cyan-400 border-b-2 border-cyan-400'
                    : 'text-gray-400 hover:text-white'
                }`}
                onClick={() => setActiveComponent('decisionAugmentation')}
              >
                {componentIcons.decisionAugmentation} {translations.components?.decisionAugmentation?.title}
              </button>
            </div>
            
            {/* Component Details */}
            <div className="bg-deep-space border border-purple-500/20 rounded-xl p-6 flex-grow">
              {activeComponent === 'realityCaptureEngine' && (
                <ul className="space-y-3">
                  {translations.components?.realityCaptureEngine?.features?.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-blue-400 mr-2">•</span>
                      <span className="text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>
              )}
              
              {activeComponent === 'simulationHypervisor' && (
                <ul className="space-y-3">
                  {translations.components?.simulationHypervisor?.features?.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-purple-400 mr-2">•</span>
                      <span className="text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>
              )}
              
              {activeComponent === 'decisionAugmentation' && (
                <ul className="space-y-3">
                  {translations.components?.decisionAugmentation?.features?.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-cyan-400 mr-2">•</span>
                      <span className="text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </motion.div>
        </div>
        
        {/* Implementation Pathway */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 40 }}
          animate={isIntersecting ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <h3 className="text-2xl font-bold mb-6 text-center text-white">{translations.implementationPathway?.title}</h3>
          
          <div className="flex flex-col md:flex-row justify-between items-start">
            {translations.implementationPathway?.steps?.map((step, index) => (
              <div key={index} className="flex-1 relative mb-8 md:mb-0">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold">
                    {index + 1}
                  </div>
                  {index < (translations.implementationPathway?.steps?.length || 0) - 1 && (
                    <div className="hidden md:block h-0.5 flex-grow bg-gradient-to-r from-purple-500 to-transparent ml-2"></div>
                  )}
                </div>
                <p className="text-gray-300 pr-4">{step}</p>
              </div>
            ))}
          </div>
        </motion.div>
        
        {/* Case Study */}
        <motion.div
          className="bg-deep-space border border-purple-500/20 rounded-xl overflow-hidden"
          initial={{ opacity: 0, y: 40 }}
          animate={isIntersecting ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <div className="grid grid-cols-1 md:grid-cols-2">
            {/* Left side - Factory visualization */}
            <div className="relative h-64 md:h-auto bg-gradient-to-br from-blue-900/30 to-purple-900/30 p-6 flex items-center justify-center">
              <div className="absolute inset-0 opacity-20 bg-[url('/images/factory-floor.jpg')] bg-cover bg-center"></div>
              <h3 className="relative text-2xl font-bold text-white text-center">
                {translations.caseStudy?.title}
              </h3>
            </div>
            
            {/* Right side - Metrics */}
            <div className="p-6">
              <h4 className="text-xl font-bold mb-4 text-white">{translations.caseStudy?.headline}</h4>
              <div className="grid grid-cols-2 gap-4">
                {translations.caseStudy?.metrics?.map((metric, index) => (
                  <div key={index} className="bg-space-black/50 rounded-lg p-4">
                    <p className="text-cyan-400 font-medium">{metric}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
