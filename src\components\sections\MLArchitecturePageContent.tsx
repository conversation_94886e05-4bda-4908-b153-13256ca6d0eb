'use client';

import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import InteractiveMLArchitecture from '@/components/ui/InteractiveMLArchitecture';
import useIntersectionObserver from '@/hooks/useIntersectionObserver';

interface MLArchitecturePageContentProps {
  translations?: {
    title?: string;
    subtitle?: string;
    exploreButton?: string;
    phases?: {
      dataIngestion?: string;
      modelTraining?: string;
      deployment?: string;
      monitoring?: string;
    };
    viewModes?: {
      conceptual?: string;
      implementation?: string;
    };
    buttons?: {
      zoomIn?: string;
      zoomOut?: string;
      fullscreen?: string;
      close?: string;
      runData?: string;
      reset?: string;
    };
  };
}

export default function MLArchitecturePageContent({ translations = {} }: MLArchitecturePageContentProps) {
  const [isInteractiveOpen, setIsInteractiveOpen] = useState(false);
  const [activePhase, setActivePhase] = useState('dataIngestion');
  const [selectedComponent, setSelectedComponent] = useState<string | null>(null);
  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 0.1, rootMargin: '0px 0px -100px 0px' });
  const sectionRef = useRef<HTMLDivElement>(null);

  // Interactive explorer translations
  const interactiveTranslations = {
    title: translations?.title || 'ML Architecture Explorer',
    subtitle: translations?.subtitle || 'Navigate through our enterprise-grade machine learning infrastructure',
    phases: {
      dataIngestion: translations?.phases?.dataIngestion || 'Data Ingestion & Preprocessing',
      modelTraining: translations?.phases?.modelTraining || 'Model Training & Evaluation',
      deployment: translations?.phases?.deployment || 'Model Deployment & Serving',
      monitoring: translations?.phases?.monitoring || 'Monitoring & Feedback',
    },
    viewModes: {
      conceptual: translations?.viewModes?.conceptual || 'Conceptual View',
      implementation: translations?.viewModes?.implementation || 'Implementation View',
    },
    buttons: {
      zoomIn: translations?.buttons?.zoomIn || 'Zoom In',
      zoomOut: translations?.buttons?.zoomOut || 'Zoom Out',
      fullscreen: translations?.buttons?.fullscreen || 'Toggle Fullscreen',
      close: translations?.buttons?.close || 'Close',
      runData: translations?.buttons?.runData || 'Run Test Data',
      reset: translations?.buttons?.reset || 'Reset View',
    },
  };

  // Architecture phases data
  const architecturePhases = {
    dataIngestion: {
      title: 'Data Ingestion & Preprocessing',
      description: 'Collect, clean, and prepare data from multiple sources for model training. Our platform handles structured and unstructured data, performs automated cleaning, and extracts relevant features.',
      icon: '📊',
      components: [
        'Data Collector',
        'Data Preprocessor',
        'Feature Selector',
        'Data Quality Monitor'
      ]
    },
    modelTraining: {
      title: 'Model Training & Evaluation',
      description: 'Train, tune, and evaluate multiple model candidates based on your business requirements. Our platform automatically selects the optimal model using key performance metrics like MAPE, RMSE, and F1 Score, saving time and improving accuracy.',
      icon: '🧠',
      components: [
        'Model Builder',
        'Model Trainer',
        'Model Evaluator',
        'Automatic Model Selector',
        'Hypothesis Executor'
      ]
    },
    deployment: {
      title: 'Model Deployment & Serving',
      description: 'Deploy trained models to production with robust, scalable infrastructure. Serve predictions through APIs, batch processes, or real-time streaming. Includes automated CI/CD pipelines for seamless model updates and versioning.',
      icon: '🚀',
      components: [
        'Model Deployer',
        'Model Predictor',
        'Kubernetes Cluster',
        'Forecast Service'
      ]
    },
    monitoring: {
      title: 'Monitoring & Feedback',
      description: 'Continuously monitor model performance, data drift, and system health. Automated alerts notify you of issues, while feedback loops enable continuous improvement and model retraining when performance degrades.',
      icon: '📈',
      components: [
        'Predictions Monitor',
        'Alert Processor',
        'Notification Service',
        'Retraining Trigger'
      ]
    }
  };

  const handleComponentClick = (component: string) => {
    setSelectedComponent(component === selectedComponent ? null : component);
  };

  const phaseVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5
      }
    })
  };

  const currentPhase = architecturePhases[activePhase as keyof typeof architecturePhases];

  // Component details mapping
  const componentDetails: Record<string, {
    description: string;
    features: string[];
    benefits: string[];
    icon: string;
  }> = {
    // Data Ingestion components
    'Data Collector': {
      description: 'Collects data from multiple sources including databases, APIs, and file systems.',
      icon: '📊',
      features: [
        'Multi-source integration',
        'Scheduled collection',
        'Data validation',
        'Incremental data loading'
      ],
      benefits: [
        'Centralized data access',
        'Reduced manual effort',
        'Consistent data format',
        'Improved data reliability'
      ]
    },
    'Data Preprocessor': {
      description: 'Cleans, transforms, and prepares raw data for model training.',
      icon: '🧹',
      features: [
        'Automated data cleaning',
        'Missing value imputation',
        'Outlier detection',
        'Data normalization'
      ],
      benefits: [
        'Higher quality training data',
        'Reduced model bias',
        'Improved model performance',
        'Standardized preprocessing pipeline'
      ]
    },
    'Feature Selector': {
      description: 'Identifies and extracts the most relevant features for model training.',
      icon: '🔍',
      features: [
        'Automated feature importance ranking',
        'Correlation analysis',
        'Dimensionality reduction',
        'Feature engineering'
      ],
      benefits: [
        'Improved model accuracy',
        'Reduced training time',
        'Lower computational requirements',
        'Better model interpretability'
      ]
    },
    'Data Quality Monitor': {
      description: 'Continuously monitors data quality and alerts on anomalies or drift.',
      icon: '📈',
      features: [
        'Real-time data validation',
        'Schema drift detection',
        'Data quality scoring',
        'Automated alerting'
      ],
      benefits: [
        'Early detection of data issues',
        'Maintained model performance',
        'Reduced production incidents',
        'Improved data governance'
      ]
    },
    // Model Training components
    'Model Builder': {
      description: 'Creates machine learning model architectures based on business requirements.',
      icon: '🏗️',
      features: [
        'Multiple algorithm support',
        'Custom architecture design',
        'AutoML capabilities',
        'Hyperparameter optimization'
      ],
      benefits: [
        'Optimized model design',
        'Reduced development time',
        'Best-practice implementations',
        'Consistent model architecture'
      ]
    },
    'Model Trainer': {
      description: 'Trains models on prepared data using distributed computing resources.',
      icon: '⚙️',
      features: [
        'Distributed training',
        'GPU acceleration',
        'Progress monitoring',
        'Checkpoint saving'
      ],
      benefits: [
        'Faster training cycles',
        'Efficient resource utilization',
        'Reproducible training runs',
        'Scalable training pipeline'
      ]
    },
    'Model Evaluator': {
      description: 'Evaluates model performance against business metrics and requirements.',
      icon: '📊',
      features: [
        'Multi-metric evaluation',
        'Cross-validation',
        'Business KPI alignment',
        'Comparative analysis'
      ],
      benefits: [
        'Objective model assessment',
        'Business-aligned evaluation',
        'Comprehensive performance insights',
        'Informed model selection'
      ]
    },
    'Automatic Model Selector': {
      description: 'Automatically selects the best performing model based on evaluation metrics.',
      icon: '🏆',
      features: [
        'Multi-model comparison',
        'Weighted metric scoring',
        'Business rule integration',
        'Champion-challenger framework'
      ],
      benefits: [
        'Objective model selection',
        'Reduced manual review',
        'Optimized business outcomes',
        'Consistent selection criteria'
      ]
    },
    'Hypothesis Executor': {
      description: 'Tests business hypotheses using trained models and statistical methods.',
      icon: '🧪',
      features: [
        'A/B test integration',
        'Statistical significance testing',
        'Hypothesis tracking',
        'Result visualization'
      ],
      benefits: [
        'Data-driven decision making',
        'Validated business assumptions',
        'Quantified business impact',
        'Improved model relevance'
      ]
    },
    // Deployment components
    'Model Deployer': {
      description: 'Deploys trained models to production environments with version control.',
      icon: '🚀',
      features: [
        'One-click deployment',
        'Canary releases',
        'Rollback capability',
        'Environment management'
      ],
      benefits: [
        'Streamlined deployment process',
        'Reduced deployment risk',
        'Version traceability',
        'Consistent deployment pipeline'
      ]
    },
    'Model Predictor': {
      description: 'Serves model predictions through APIs with low-latency response times.',
      icon: '🔮',
      features: [
        'RESTful API endpoints',
        'Batch prediction support',
        'Request validation',
        'Response caching'
      ],
      benefits: [
        'Consistent prediction interface',
        'Scalable prediction serving',
        'Optimized response times',
        'Flexible integration options'
      ]
    },
    'Kubernetes Cluster': {
      description: 'Manages containerized model deployments with auto-scaling capabilities.',
      icon: '☸️',
      features: [
        'Auto-scaling',
        'Load balancing',
        'Health monitoring',
        'Resource optimization'
      ],
      benefits: [
        'High availability',
        'Cost-efficient resource usage',
        'Simplified operations',
        'Consistent deployment environment'
      ]
    },
    'Forecast Service': {
      description: 'Generates time-series forecasts for business planning and decision making.',
      icon: '📅',
      features: [
        'Scheduled forecasting',
        'Multiple time horizons',
        'Confidence intervals',
        'Scenario analysis'
      ],
      benefits: [
        'Proactive business planning',
        'Improved resource allocation',
        'Reduced forecast error',
        'Automated reporting'
      ]
    },
    // Monitoring components
    'Predictions Monitor': {
      description: 'Monitors prediction quality and model performance in production.',
      icon: '📊',
      features: [
        'Prediction accuracy tracking',
        'Performance degradation detection',
        'Concept drift monitoring',
        'Custom metric dashboards'
      ],
      benefits: [
        'Early detection of model issues',
        'Maintained prediction quality',
        'Extended model lifespan',
        'Transparent model performance'
      ]
    },
    'Alert Processor': {
      description: 'Processes and routes alerts based on monitoring thresholds and rules.',
      icon: '🚨',
      features: [
        'Customizable alert thresholds',
        'Alert prioritization',
        'Intelligent routing',
        'Alert aggregation'
      ],
      benefits: [
        'Reduced alert fatigue',
        'Faster incident response',
        'Improved operational efficiency',
        'Proactive issue resolution'
      ]
    },
    'Notification Service': {
      description: 'Delivers notifications through multiple channels based on alert severity.',
      icon: '📱',
      features: [
        'Multi-channel delivery',
        'Customizable templates',
        'Delivery confirmation',
        'Escalation paths'
      ],
      benefits: [
        'Timely issue awareness',
        'Appropriate stakeholder engagement',
        'Reduced mean time to respond',
        'Consistent communication'
      ]
    },
    'Retraining Trigger': {
      description: 'Automatically triggers model retraining when performance degrades.',
      icon: '🔄',
      features: [
        'Performance-based triggers',
        'Scheduled retraining',
        'Data drift detection',
        'Training pipeline integration'
      ],
      benefits: [
        'Maintained model performance',
        'Reduced manual intervention',
        'Adaptation to changing patterns',
        'Extended model relevance'
      ]
    }
  };

  return (
    <section
      ref={sectionRef}
      className="relative bg-black pt-32 pb-16"
      id="ml-architecture"
    >
      {/* Hero Section */}
      <div className="container mx-auto px-4 mb-0">
        <motion.div
          className="text-center max-w-4xl mx-auto"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-500">
            Machine Learning Architecture
          </h2>
          <p className="text-lg text-gray-300 mb-8">
            Enterprise-grade ML infrastructure for data ingestion, model training, deployment, and monitoring.
          </p>
          <motion.button
            className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white font-medium py-3 px-6 rounded-lg shadow-lg shadow-purple-500/20 mb-12"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.97 }}
            onClick={() => setIsInteractiveOpen(true)}
          >
            Launch Interactive Explorer
          </motion.button>
        </motion.div>
      </div>

      <div className="py-4"></div>

      {/* Phase Selection Tabs */}
      <div className="sticky top-20 z-30 bg-black/80 backdrop-blur-md py-4 border-b border-gray-800">
        <div className="container mx-auto px-4">
          <nav className="flex flex-wrap justify-center gap-2 md:gap-8">
            {Object.entries(architecturePhases).map(([phase, content]) => (
              <button
                key={phase}
                onClick={() => setActivePhase(phase)}
                className={`relative px-4 py-2 text-button font-medium transition-all duration-300 ${
                  activePhase === phase
                    ? 'text-cyan-400'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                {content.title}
                {activePhase === phase && (
                  <motion.div
                    className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-cyan-400 to-purple-500"
                    layoutId="activeSection"
                    transition={{ type: 'spring', stiffness: 380, damping: 30 }}
                  />
                )}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* ML Architecture Content */}
      <div ref={ref} className="container mx-auto px-4 py-12">
        {/* Active Phase Content */}
        <motion.div
          className="bg-gray-900/40 backdrop-blur-sm rounded-xl shadow-lg border border-purple-700/30 p-8 md:p-12 w-full mx-auto"
          key={activePhase}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex flex-col md:flex-row items-start gap-6">
            <div className="text-cyan-400 text-3xl flex-shrink-0">
              {currentPhase.icon}
            </div>

            <div className="flex-grow">
              <h3 className="text-2xl font-bold mb-4 text-white">
                {currentPhase.title}
              </h3>
              <p className="text-lg text-gray-300 mb-6">
                {currentPhase.description}
              </p>

              <h4 className="text-xl font-semibold mb-4 text-cyan-400">Key Components</h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {currentPhase.components.map((component, idx) => (
                  <motion.div
                    key={component}
                    className={`bg-gray-800/50 border ${
                      selectedComponent === component
                        ? 'border-cyan-500 ring-1 ring-cyan-500/50'
                        : component === 'Data Collector' && activePhase === 'dataIngestion'
                          ? 'border-purple-500/40'
                          : 'border-purple-500/20'
                    } rounded-lg p-4 cursor-pointer transition-all hover:bg-gray-800/80 relative`}
                    custom={idx}
                    variants={phaseVariants}
                    initial="hidden"
                    animate={isIntersecting ? "visible" : "hidden"}
                    onClick={() => handleComponentClick(component)}
                    whileHover={{ scale: 1.02 }}
                  >
                    <span className="text-white font-medium">{component}</span>
                  </motion.div>
                ))}
              </div>

              {/* Component Details Section */}
              {selectedComponent && componentDetails[selectedComponent] && (
                <motion.div
                  className="mt-6 bg-gray-800/30 border border-cyan-500/30 rounded-lg p-6"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="flex items-start gap-4">
                    <div className="text-3xl bg-gradient-to-br from-cyan-400 to-purple-500 rounded-full h-14 w-14 flex items-center justify-center">
                      <span>{componentDetails[selectedComponent].icon}</span>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold mb-3 text-white">{selectedComponent}</h3>
                      <p className="text-gray-300">{componentDetails[selectedComponent].description}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <div className="p-4 bg-black/50 rounded-lg border border-gray-800">
                      <h4 className="font-semibold text-cyan-400 mb-3">Key Features</h4>
                      <ul className="space-y-2 text-gray-300">
                        {componentDetails[selectedComponent].features.map((feature, index) => (
                          <li key={`feature-${index}`} className="flex items-start">
                            <span className="text-cyan-400 mr-2">→</span>
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div className="p-4 bg-black/50 rounded-lg border border-gray-800">
                      <h4 className="font-semibold text-cyan-400 mb-3">Benefits</h4>
                      <ul className="space-y-2 text-gray-300">
                        {componentDetails[selectedComponent].benefits.map((benefit, index) => (
                          <li key={`benefit-${index}`} className="flex items-start">
                            <span className="text-cyan-400 mr-2">→</span>
                            <span>{benefit}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </motion.div>
              )}
            </div>
          </div>
        </motion.div>
      </div>

      {/* Interactive ML Architecture */}
      <InteractiveMLArchitecture
        isOpen={isInteractiveOpen}
        onClose={() => setIsInteractiveOpen(false)}
        translations={interactiveTranslations}
      />

      {/* Features Section */}
      <div className="container mx-auto px-4 mt-16">
        <h2 className="text-3xl font-bold mb-8 text-center text-white">
          Key Features of Our ML Architecture
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Feature 1 */}
          <motion.div
            className="bg-gray-800/50 border border-purple-500/20 rounded-lg p-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="text-cyan-400 text-2xl mb-1">🔄</div>
            <h3 className="text-xl font-semibold mb-1 text-white">End-to-End Automation</h3>
            <p className="text-gray-300">
              Fully automated pipeline from data ingestion to model deployment and monitoring, reducing manual intervention and human error.
            </p>
          </motion.div>

          {/* Feature 2 */}
          <motion.div
            className="bg-gray-800/50 border border-purple-500/20 rounded-lg p-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
          >
            <div className="text-cyan-400 text-2xl mb-1">⚡</div>
            <h3 className="text-xl font-semibold mb-1 text-white">Scalable Infrastructure</h3>
            <p className="text-gray-300">
              Cloud-native architecture that scales horizontally to handle varying workloads, from small datasets to enterprise-scale big data.
            </p>
          </motion.div>

          {/* Feature 3 */}
          <motion.div
            className="bg-gray-800/50 border border-purple-500/20 rounded-lg p-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <div className="text-cyan-400 text-2xl mb-1">🔍</div>
            <h3 className="text-xl font-semibold mb-1 text-white">Intelligent Model Selection</h3>
            <p className="text-gray-300">
              Automatic evaluation and selection of the best-performing models based on business-specific metrics and requirements.
            </p>
          </motion.div>

          {/* Feature 4 */}
          <motion.div
            className="bg-gray-800/50 border border-purple-500/20 rounded-lg p-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <div className="text-cyan-400 text-2xl mb-1">📊</div>
            <h3 className="text-xl font-semibold mb-1 text-white">Comprehensive Monitoring</h3>
            <p className="text-gray-300">
              Real-time monitoring of model performance, data quality, and system health with automated alerts and remediation.
            </p>
          </motion.div>

          {/* Feature 5 */}
          <motion.div
            className="bg-gray-800/50 border border-purple-500/20 rounded-lg p-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <div className="text-cyan-400 text-2xl mb-1">🔒</div>
            <h3 className="text-xl font-semibold mb-1 text-white">Enterprise Security</h3>
            <p className="text-gray-300">
              Built-in security at every layer, including data encryption, access controls, and compliance with industry regulations.
            </p>
          </motion.div>

          {/* Feature 6 */}
          <motion.div
            className="bg-gray-800/50 border border-purple-500/20 rounded-lg p-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="text-cyan-400 text-2xl mb-1">📆</div>
            <h3 className="text-xl font-semibold mb-1 text-white">Scheduled Predictions</h3>
            <p className="text-gray-300">
              Automated scheduling of model predictions for time-series forecasting and proactive business insights.
            </p>
          </motion.div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="container mx-auto px-4 mt-16 mb-16 text-center">
        <h2 className="text-3xl font-bold mb-4 text-white">
          Ready to Transform Your ML Operations?
        </h2>
        <p className="text-xl text-gray-300 mb-6 max-w-3xl mx-auto">
          Our enterprise-grade ML architecture can be customized to your specific business needs and integrated with your existing systems.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <motion.button
            className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white font-medium py-3 px-6 rounded-lg shadow-lg shadow-purple-500/20"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.97 }}
            onClick={() => setIsInteractiveOpen(true)}
          >
            Explore Interactive Architecture
          </motion.button>
          <motion.a
            href="/contact"
            className="bg-transparent border border-cyan-500 text-cyan-400 font-medium py-3 px-6 rounded-lg hover:bg-cyan-900/20 transition-colors"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.97 }}
          >
            Contact Our Team
          </motion.a>
        </div>
      </div>
    </section>
  );
}
