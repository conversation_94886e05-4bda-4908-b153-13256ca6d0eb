'use client';

import React, { useEffect, useRef } from 'react';

interface ParticleBackgroundProps {
  color?: string;
  secondaryColor?: string;
  density?: 'low' | 'medium' | 'high';
  speed?: 'slow' | 'medium' | 'fast';
  className?: string;
}

interface Particle {
  x: number;
  y: number;
  size: number;
  speedX: number;
  speedY: number;
  color: string;
  opacity: number;
}

const ParticleBackground: React.FC<ParticleBackgroundProps> = ({
  color = '#4FC3F7',
  secondaryColor = '#7C4DFF',
  density = 'medium',
  speed = 'medium',
  className = ''
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const particlesRef = useRef<Particle[]>([]);
  const animationRef = useRef<number>(0);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Particle density based on prop
    const densityMap = {
      low: 30,
      medium: 60,
      high: 100
    };

    const particleCount = densityMap[density];

    // Speed multiplier based on prop
    const speedMap = {
      slow: 0.3,
      medium: 0.6,
      fast: 1.2
    };

    const speedMultiplier = speedMap[speed];

    // Initialize particles
    const initParticles = () => {
      const particles: Particle[] = [];

      for (let i = 0; i < particleCount; i++) {
        const x = Math.random() * canvas.width;
        const y = Math.random() * canvas.height;
        const size = Math.random() * 2 + 1;
        const particleColor = Math.random() > 0.5 ? color : secondaryColor;

        particles.push({
          x,
          y,
          size,
          speedX: (Math.random() - 0.5) * speedMultiplier,
          speedY: (Math.random() - 0.5) * speedMultiplier,
          color: particleColor,
          opacity: Math.random() * 0.5 + 0.2
        });
      }

      particlesRef.current = particles;
    };

    // Set canvas dimensions
    const resizeCanvas = () => {
      const parent = canvas.parentElement;
      if (!parent) return;

      canvas.width = parent.clientWidth;
      canvas.height = parent.clientHeight;

      // Reinitialize particles when canvas is resized
      initParticles();
    };

    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      particlesRef.current.forEach(particle => {
        // Update position
        particle.x += particle.speedX;
        particle.y += particle.speedY;

        // Bounce off edges
        if (particle.x < 0 || particle.x > canvas.width) {
          particle.speedX = -particle.speedX;
        }

        if (particle.y < 0 || particle.y > canvas.height) {
          particle.speedY = -particle.speedY;
        }

        // Draw particle
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.globalAlpha = particle.opacity;
        ctx.fill();
        ctx.globalAlpha = 1;
      });

      animationRef.current = requestAnimationFrame(animate);
    };

    // Initialize on mount and when window is resized
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Start animation
    animate();

    // Cleanup
    return () => {
      window.removeEventListener('resize', resizeCanvas);
      cancelAnimationFrame(animationRef.current);
    };
  }, [color, secondaryColor, density, speed]);

  return (
    <canvas
      ref={canvasRef}
      className={`absolute inset-0 w-full h-full ${className}`}
    />
  );
};

export default ParticleBackground;
