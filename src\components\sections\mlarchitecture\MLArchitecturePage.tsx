'use client';

import { useState, useEffect, useRef, ReactNode } from 'react';
import { motion } from 'framer-motion';
import DataIngestionSection from '@/components/sections/mlarchitecture/DataIngestionSection';
import ModelTrainingSection from '@/components/sections/mlarchitecture/ModelTrainingSection';
import DeploymentSection from '@/components/sections/mlarchitecture/DeploymentSection';
import MonitoringSection from '@/components/sections/mlarchitecture/MonitoringSection';

// Component that resets scroll position when it mounts
const ScrollResetter = ({ children }: { children: ReactNode }) => {
  // This component will be remounted whenever the key changes
  // When it mounts, it will be at the top of its container
  return <div className="scroll-resetter">{children}</div>;
};

type Phase = {
  id: string;
  title: string;
};

type MLArchitecturePageProps = {
  translations: {
    title: string;
    pageTitle: string;
    pageSubtitle: string;
    phases: Phase[];
    pageContent?: {
      title?: string;
      subtitle?: string;
      launchExplorer?: string;
      keyComponents?: string;
      componentDetailsLabel?: string;
      noDescriptionAvailable?: string;
      architecturePhases?: {
        dataIngestion?: {
          title?: string;
          description?: string;
          icon?: string;
          components?: string[];
        };
        modelTraining?: {
          title?: string;
          description?: string;
          icon?: string;
          components?: string[];
        };
        deployment?: {
          title?: string;
          description?: string;
          icon?: string;
          components?: string[];
        };
        monitoring?: {
          title?: string;
          description?: string;
          icon?: string;
          components?: string[];
        };
      };
      componentNames?: any;
      componentDetails?: any;
      keyFeaturesSection?: any;
      ctaSection?: any;
    };
  };
};

export default function MLArchitecturePage({ translations }: MLArchitecturePageProps) {
  const [activePhase, setActivePhase] = useState<string>('dataIngestion');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const contentSectionRef = useRef<HTMLDivElement>(null);

  // Debug logging for translations
  useEffect(() => {
    console.log('MLArchitecturePage - translations:', translations);
    console.log('MLArchitecturePage - componentDetails:', translations.pageContent?.componentDetails);
  }, [translations]);

  // Function to handle phase change
  const handlePhaseChange = (phaseId: string) => {
    // Set the new active phase
    setActivePhase(phaseId);

    // After the phase changes, scroll the content section to top (but keep nav visible)
    setTimeout(() => {
      if (contentSectionRef.current) {
        // Only scroll if the nav is visible (i.e., user has scrolled past header)
        const nav = document.getElementById('ml-architecture-navigation');
        if (nav) {
          const navRect = nav.getBoundingClientRect();
          const navStickyTop = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--nav-sticky-top') || '0', 10);
          // If nav is at or near the top of the viewport, scroll content just below it
          if (navRect.top >= 10 && navRect.bottom > 0) {
            // Scroll the content section so its top aligns just below the nav
            const contentRect = contentSectionRef.current.getBoundingClientRect();
            const scrollY = window.scrollY + contentRect.top - navRect.height - navStickyTop;
            window.scrollTo({ top: scrollY, behavior: 'instant' in window ? 'instant' : 'auto' });
          }
        }
      }
    }, 0);
  };

  // Log when active phase changes
  useEffect(() => {
    console.log('Active phase is now:', activePhase);
  }, [activePhase]);

  // Render the active phase based on state
  const renderActivePhase = () => {
    // Check if translations.pageContent?.componentDetails exists
    const originalComponentDetails = translations.pageContent?.componentDetails;
    console.log('renderActivePhase - originalComponentDetails:', originalComponentDetails);

    // Check a specific component detail to see if it's properly structured
    if (originalComponentDetails) {
      console.log('Sample component detail (modelBuilder):', originalComponentDetails.modelBuilder);
    }

    // Create a processedComponentDetails object with the correct structure
    const processedComponentDetails = {};

    // Check if originalComponentDetails exists
    if (originalComponentDetails) {
      // Loop through all component keys
      const componentKeys = [
        'dataCollector', 'dataPreprocessor', 'featureSelector', 'dataQualityMonitor',
        'modelBuilder', 'modelTrainer', 'modelEvaluator', 'automaticModelSelector',
        'hypothesisExecutor', 'modelDeployer', 'modelPredictor', 'kubernetesCluster',
        'forecastService', 'predictionsMonitor', 'alertProcessor', 'notificationService',
        'retrainingTrigger'
      ];

      // Add each component's details to the processedComponentDetails object
      componentKeys.forEach(key => {
        if (originalComponentDetails[key]) {
          processedComponentDetails[key] = {
            description: originalComponentDetails[key].description || `Description for ${key}`
          };
        }
      });
    }

    // If no component details were found, use hardcoded ones for testing
    if (Object.keys(processedComponentDetails).length === 0) {
      console.warn('No component details found in translations, using hardcoded ones');

      // Hardcoded component details as fallback
      Object.assign(processedComponentDetails, {
        dataCollector: {
          description: "Test description for Data Collector component. This is a placeholder to test if descriptions are displayed correctly."
        },
        dataPreprocessor: {
          description: "Test description for Data Preprocessor component. This is a placeholder to test if descriptions are displayed correctly."
        },
        featureSelector: {
          description: "Test description for Feature Selector component. This is a placeholder to test if descriptions are displayed correctly."
        },
        dataQualityMonitor: {
          description: "Test description for Data Quality Monitor component. This is a placeholder to test if descriptions are displayed correctly."
        },
        modelBuilder: {
          description: "Test description for Model Builder component. This is a placeholder to test if descriptions are displayed correctly."
        },
        modelTrainer: {
          description: "Test description for Model Trainer component. This is a placeholder to test if descriptions are displayed correctly."
        }
      });
    }

    switch (activePhase) {
      case 'dataIngestion':
        return <DataIngestionSection
          translations={translations.pageContent?.architecturePhases?.dataIngestion}
          componentNames={translations.pageContent?.componentNames}
          componentDetails={processedComponentDetails}
        />;
      case 'modelTraining':
        return <ModelTrainingSection
          translations={translations.pageContent?.architecturePhases?.modelTraining}
          componentNames={translations.pageContent?.componentNames}
          componentDetails={processedComponentDetails}
        />;
      case 'deployment':
        return <DeploymentSection
          translations={translations.pageContent?.architecturePhases?.deployment}
          componentNames={translations.pageContent?.componentNames}
          componentDetails={processedComponentDetails}
        />;
      case 'monitoring':
        return <MonitoringSection
          translations={translations.pageContent?.architecturePhases?.monitoring}
          componentNames={translations.pageContent?.componentNames}
          componentDetails={processedComponentDetails}
        />;
      default:
        return <DataIngestionSection
          translations={translations.pageContent?.architecturePhases?.dataIngestion}
          componentNames={translations.pageContent?.componentNames}
          componentDetails={processedComponentDetails}
        />;
    }
  };

  // Navigation component
  const MLArchitectureNavigation = () => (
    <div
      id="ml-architecture-navigation"
      className="sticky w-full left-0 right-0 bg-black/90 backdrop-blur-lg py-3 border-b border-gray-800"
      style={{
        position: 'sticky',
        top: 'var(--nav-sticky-top)',
        zIndex: 'var(--service-nav-z-index)'
      }}
    >
      <div className="container mx-auto px-4">
        {/* Mobile dropdown for smaller screens */}
        <div className="block md:hidden">
          <div
            className="flex items-center justify-between py-2"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <span className="text-cyan-400 font-medium">
              {translations.phases.find(p => p.id === activePhase)?.title || 'Select Phase'}
            </span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`h-5 w-5 text-cyan-400 transition-transform ${isMobileMenuOpen ? 'transform rotate-180' : ''}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>

          {/* Mobile dropdown menu */}
          {isMobileMenuOpen && (
            <div className="absolute left-0 right-0 mt-1 py-2 px-4 bg-gray-900/95 backdrop-blur-lg shadow-lg z-50">
              {translations.phases.map((phase) => (
                <div
                  key={phase.id}
                  className="relative"
                  onClick={() => {
                    handlePhaseChange(phase.id);
                    setIsMobileMenuOpen(false);
                  }}
                >
                  <div
                    className={`px-4 py-3 text-base font-medium transition-all duration-300 cursor-pointer ${
                      activePhase === phase.id
                        ? 'text-cyan-400'
                        : 'text-gray-400 hover:text-white'
                    }`}
                  >
                    {phase.title}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Desktop navigation */}
        <div className="hidden md:block">
          <div className="flex flex-wrap justify-center gap-2 md:gap-8">
            {translations.phases.map((phase) => (
              <div
                key={phase.id}
                className="relative"
              >
                <div
                  onClick={() => {
                    console.log('Clicked on phase:', phase.id);
                    handlePhaseChange(phase.id);
                  }}
                  className={`px-4 py-2 text-button font-medium transition-all duration-300 cursor-pointer ${
                    activePhase === phase.id
                      ? 'text-cyan-400'
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  {phase.title}
                </div>
                {activePhase === phase.id && (
                  <motion.div
                    className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-cyan-400 to-purple-500"
                    layoutId="activeSection"
                    transition={{ type: 'spring', stiffness: 380, damping: 30 }}
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <>
      {/* Header section with title and description */}
      <section className="relative bg-black pt-32 pb-8 ml-architecture-page">
        <div className="container mx-auto px-4">
          <motion.h2
            className="text-3xl md:text-4xl font-bold mb-6 text-center bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-500"
            initial={{ opacity: 0, y: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            {translations.pageTitle}
          </motion.h2>

          <motion.p
            className="text-lg text-gray-300 max-w-3xl mx-auto text-center mb-4"
            initial={{ opacity: 0, y: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {translations.pageSubtitle}
          </motion.p>
        </div>
      </section>

      {/* Integrated navigation section */}
      <MLArchitectureNavigation />

      {/* Content section */}
      <section className="relative bg-black pb-20">
        <div className="container mx-auto px-4" style={{ zIndex: 'var(--content-z-index)' }}>
          {/* Active phase section - using key to force remount when phase changes */}
          <div id="ml-architecture-content" className="w-full pt-8" ref={contentSectionRef}>
            {/* Using key to force the component to remount when active phase changes */}
            <ScrollResetter key={activePhase}>
              {renderActivePhase()}
            </ScrollResetter>
          </div>
        </div>
      </section>
    </>
  );
}
