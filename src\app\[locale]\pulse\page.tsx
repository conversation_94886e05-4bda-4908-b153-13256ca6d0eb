// src/app/[locale]/pulse/page.tsx
import { getServerTranslations } from '@/lib/i18n';
import PulseSection from '@/components/sections/pulse/PulseSection';

export default async function PulsePage({ params }: { params: { locale: string } | Promise<{ locale: string }> }) {
  // Handle both Promise and direct object cases
  const resolvedParams = params instanceof Promise ? await params : params

  // Get translations on the server
  const { t } = await getServerTranslations(resolvedParams.locale, ['language']);

  // Prepare translations to pass as props
  const translations = {
    title: t('pulse.title'),
    subtitle: t('pulse.subtitle'),
    howToUseTitle: t('pulse.howToUseTitle'),
    howToUseStep1Title: t('pulse.howToUseStep1Title'),
    howToUseStep1Desc: t('pulse.howToUseStep1Desc'),
    howToUseStep2Title: t('pulse.howToUseStep2Title'),
    howToUseStep2Desc: t('pulse.howToUseStep2Desc'),
    howToUseStep3Title: t('pulse.howToUseStep3Title'),
    howToUseStep3Desc: t('pulse.howToUseStep3Desc'),
    timeRangeSelector: {
      title: t('pulse.timeRangeSelector.title'),
      options: {
        sixMonths: t('pulse.timeRangeSelector.options.sixMonths'),
        sixMonthsDesc: t('pulse.timeRangeSelector.options.sixMonthsDesc'),
        oneYear: t('pulse.timeRangeSelector.options.oneYear'),
        oneYearDesc: t('pulse.timeRangeSelector.options.oneYearDesc'),
        fiveYears: t('pulse.timeRangeSelector.options.fiveYears'),
        fiveYearsDesc: t('pulse.timeRangeSelector.options.fiveYearsDesc')
      }
    },
    filters: {
      title: t('pulse.filters.title'),
      description: t('pulse.filters.description'),
      options: {
        ai: t('pulse.filters.options.ai'),
        aiDesc: t('pulse.filters.options.aiDesc'),
        cloud: t('pulse.filters.options.cloud'),
        cloudDesc: t('pulse.filters.options.cloudDesc'),
        iot: t('pulse.filters.options.iot'),
        iotDesc: t('pulse.filters.options.iotDesc'),
        security: t('pulse.filters.options.security'),
        securityDesc: t('pulse.filters.options.securityDesc'),
        blockchain: t('pulse.filters.options.blockchain'),
        blockchainDesc: t('pulse.filters.options.blockchainDesc')
      }
    },
    maturityLevels: {
      established: t('pulse.maturityLevels.established'),
      maturing: t('pulse.maturityLevels.maturing'),
      growing: t('pulse.maturityLevels.growing'),
      emerging: t('pulse.maturityLevels.emerging'),
      descriptions: {
        established: t('pulse.maturityLevels.descriptions.established'),
        maturing: t('pulse.maturityLevels.descriptions.maturing'),
        growing: t('pulse.maturityLevels.descriptions.growing'),
        emerging: t('pulse.maturityLevels.descriptions.emerging')
      }
    },
    timelineTitle: t('pulse.timelineTitle'),
    timelineDescription: t('pulse.timelineDescription'),
    noTechnologiesMessage: t('pulse.noTechnologiesMessage'),
    resetFiltersLabel: t('pulse.resetFiltersLabel'),
    technologiesLabel: t('pulse.technologiesLabel'),
    technologies: {
      // AI Technologies
      generativeAI: {
        name: t('pulse.technologies.generativeAI.name'),
        description: t('pulse.technologies.generativeAI.description'),
        maturity: 'growing' as 'growing',
        category: 'ai',
        adoptionRate: t('pulse.technologies.generativeAI.adoptionRate'),
        industryImpact: {
          healthcare: t('pulse.technologies.generativeAI.industryImpact.healthcare'),
          finance: t('pulse.technologies.generativeAI.industryImpact.finance'),
          manufacturing: t('pulse.technologies.generativeAI.industryImpact.manufacturing'),
          retail: t('pulse.technologies.generativeAI.industryImpact.retail')
        }
      },
      quantumML: {
        name: t('pulse.technologies.quantumML.name'),
        description: t('pulse.technologies.quantumML.description'),
        maturity: 'emerging' as 'emerging',
        category: 'ai',
        adoptionRate: t('pulse.technologies.quantumML.adoptionRate'),
        industryImpact: {
          healthcare: t('pulse.technologies.quantumML.industryImpact.healthcare'),
          finance: t('pulse.technologies.quantumML.industryImpact.finance'),
          manufacturing: t('pulse.technologies.quantumML.industryImpact.manufacturing'),
          research: t('pulse.technologies.quantumML.industryImpact.research')
        }
      },
      // Cloud Technologies
      serverlessPlatforms: {
        name: t('pulse.technologies.serverlessPlatforms.name'),
        description: t('pulse.technologies.serverlessPlatforms.description'),
        maturity: 'maturing' as 'maturing',
        category: 'cloud',
        adoptionRate: t('pulse.technologies.serverlessPlatforms.adoptionRate'),
        industryImpact: {
          software: t('pulse.technologies.serverlessPlatforms.industryImpact.software'),
          finance: t('pulse.technologies.serverlessPlatforms.industryImpact.finance'),
          retail: t('pulse.technologies.serverlessPlatforms.industryImpact.retail'),
          healthcare: t('pulse.technologies.serverlessPlatforms.industryImpact.healthcare')
        }
      },
      // Security Technologies
      zeroTrust: {
        name: t('pulse.technologies.zeroTrust.name'),
        description: t('pulse.technologies.zeroTrust.description'),
        maturity: 'growing' as 'growing',
        category: 'security',
        adoptionRate: t('pulse.technologies.zeroTrust.adoptionRate'),
        industryImpact: {
          finance: t('pulse.technologies.zeroTrust.industryImpact.finance'),
          healthcare: t('pulse.technologies.zeroTrust.industryImpact.healthcare'),
          government: t('pulse.technologies.zeroTrust.industryImpact.government'),
          retail: t('pulse.technologies.zeroTrust.industryImpact.retail')
        }
      }
    },
    relevanceMeter: {
      title: t('pulse.relevanceMeter.title'),
      companySize: {
        title: t('pulse.relevanceMeter.companySize.title'),
        options: {
          startup: t('pulse.relevanceMeter.companySize.options.startup'),
          smb: t('pulse.relevanceMeter.companySize.options.smb'),
          enterprise: t('pulse.relevanceMeter.companySize.options.enterprise')
        }
      },
      industry: {
        title: t('pulse.relevanceMeter.industry.title'),
        options: {
          healthcare: t('pulse.relevanceMeter.industry.options.healthcare'),
          finance: t('pulse.relevanceMeter.industry.options.finance'),
          retail: t('pulse.relevanceMeter.industry.options.retail'),
          manufacturing: t('pulse.relevanceMeter.industry.options.manufacturing'),
          technology: t('pulse.relevanceMeter.industry.options.technology')
        }
      },
      cta: t('pulse.relevanceMeter.cta')
    }
  };

  return <PulseSection translations={translations} />;
}
