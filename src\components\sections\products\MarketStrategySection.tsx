'use client';

import { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import useIntersectionObserver from '@/hooks/useIntersectionObserver';
import ProcessDetails from '@/components/sections/products/ProcessDetails';

interface MarketStrategySectionProps {
  translations?: {
    managementPhases?: {
      marketStrategy?: {
        title?: string;
        description?: string;
        processes?: {
          productPositioning?: string;
          competitorAnalysis?: string;
          licensingStrategy?: string;
          roadmapPlanning?: string;
          yearlyPlanning?: string;
        };
      };
    };
    components?: {
      keyProcesses?: string;
      processDetailsContent?: {
        [key: string]: {
          description?: string;
          features?: string[];
        };
      };
    };
  };
}

export default function MarketStrategySection({ translations }: MarketStrategySectionProps) {
  const [selectedProcess, setSelectedProcess] = useState<string | null>(null);
  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 0.3 });
  const processDetailsRef = useRef<HTMLDivElement>(null);

  // Set initial process to Roadmap Planning
  useEffect(() => {
    if (isIntersecting) {
      const timer = setTimeout(() => {
        setSelectedProcess('Roadmap Planning');
      }, 800);

      return () => clearTimeout(timer);
    }
  }, [isIntersecting]);

  const handleProcessClick = (process: string) => {
    setSelectedProcess(process === selectedProcess ? null : process);
    
    // Scroll to process details when a process is selected
    if (process !== selectedProcess && processDetailsRef.current) {
      setTimeout(() => {
        processDetailsRef.current?.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      }, 100);
    }
  };

  const processes = [
    translations?.managementPhases?.marketStrategy?.processes?.productPositioning || 'Product Positioning',
    translations?.managementPhases?.marketStrategy?.processes?.competitorAnalysis || 'Competitor Analysis',
    translations?.managementPhases?.marketStrategy?.processes?.licensingStrategy || 'Licensing Strategy',
    translations?.managementPhases?.marketStrategy?.processes?.roadmapPlanning || 'Roadmap Planning',
    translations?.managementPhases?.marketStrategy?.processes?.yearlyPlanning || 'Yearly P&L Planning'
  ];

  const phaseVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5
      }
    })
  };

  return (
    <div ref={ref} className="relative">
      <motion.div
        className="bg-gray-900/40 backdrop-blur-sm rounded-xl shadow-lg border border-purple-700/30 p-8 md:p-12 max-w-5xl mx-auto"
        initial={{ opacity: 0, y: 20 }}
        animate={isIntersecting ? { opacity: 1, y: 0 } : {}}
        transition={{ duration: 0.6 }}
      >
        <div className="flex flex-col md:flex-row items-start gap-8">
          <div className="text-cyan-400 flex-shrink-0">
            <svg className="w-12 h-12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
              <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 3v11.25A2.25 2.25 0 006 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0118 16.5h-2.25m-7.5 0h7.5m-7.5 0l-1 3m8.5-3l1 3m0 0l.5 1.5m-.5-1.5h-9.5m0 0l-.5 1.5m.75-9l3-3 2.148 2.148A12.061 12.061 0 0116.5 7.605" />
            </svg>
          </div>

          <div className="flex-grow">
            <h2 className="text-3xl font-bold mb-4 text-white">
              {translations?.managementPhases?.marketStrategy?.title || 'Market Strategy & Planning'}
            </h2>
            <p className="text-lg text-gray-300 mb-8">
              {translations?.managementPhases?.marketStrategy?.description || "Define your product's market position, competitive advantage, and go-to-market strategy. Our platform helps you analyze competitors, develop licensing strategies, and create comprehensive roadmaps for successful market entry and growth."}
            </p>

            <h3 className="text-xl font-semibold mb-4 text-cyan-400">
              {translations?.components?.keyProcesses || 'Key Processes'}
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {processes.map((process, idx) => (
                <motion.div
                  key={process}
                  className={`bg-gray-800/50 border ${
                    selectedProcess === process
                      ? 'border-cyan-500 ring-1 ring-cyan-500/50'
                      : process === 'Roadmap Planning'
                        ? 'border-purple-500/40'
                        : 'border-purple-500/20'
                  } rounded-lg p-4 cursor-pointer transition-all hover:bg-gray-800/80 hover:border-cyan-500/50 hover:shadow-lg hover:shadow-cyan-500/10 relative group`}
                  custom={idx}
                  variants={phaseVariants}
                  initial="hidden"
                  animate={isIntersecting ? "visible" : "hidden"}
                  onClick={() => handleProcessClick(process)}
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="flex justify-between items-center">
                    <span className="text-white font-medium">{process}</span>
                    <span className="text-cyan-400 opacity-0 group-hover:opacity-100 transition-opacity flex items-center">
                      <span className="text-xs mr-1">View details</span>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </span>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Process Details Section */}
            {selectedProcess && (
              <div ref={processDetailsRef}>
                <ProcessDetails 
                  process={selectedProcess} 
                  translations={translations} 
                  onClose={() => setSelectedProcess(null)} 
                />
              </div>
            )}
          </div>
        </div>
      </motion.div>
    </div>
  );
}
