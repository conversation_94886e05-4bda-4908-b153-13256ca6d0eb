'use client';

import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useAnimationContext } from '@/context/AnimationContext';
import useIntersectionObserver from '@/hooks/useIntersectionObserver';
import ServiceCard from '@/components/ui/ServiceCard';
import { Brain, ChartLine, Shield, Sliders, Layers, BarChart3 } from 'lucide-react';

type CryptoBotTranslations = {
  title?: string;
  subtitle?: string;
  overview?: {
    title?: string;
    description?: string;
  };
  techStack?: {
    title?: string;
    components?: {
      optimizationEngine?: {
        title?: string;
        description?: string;
      };
      featureSelection?: {
        title?: string;
        description?: string;
      };
      parameterOptimization?: {
        title?: string;
        description?: string;
      };
      technicalIndicators?: {
        title?: string;
        description?: string;
      };
      riskManagement?: {
        title?: string;
        description?: string;
      };
      performanceMetrics?: {
        title?: string;
        description?: string;
      };
    };
  };
};

type CryptoBotSectionProps = {
  translations?: CryptoBotTranslations;
};

export default function CryptoBotSection({ translations }: CryptoBotSectionProps) {
  const { setActiveSection } = useAnimationContext();
  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 0.2 });
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (isIntersecting) {
      setActiveSection('labs');
    }
  }, [isIntersecting, setActiveSection]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    show: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  // Technology stack components with icons
  const techStackComponents = [
    {
      title: translations?.techStack?.components?.optimizationEngine?.title || 'Optimization Engine',
      icon: Brain,
      description: translations?.techStack?.components?.optimizationEngine?.description || 'Advanced reinforcement learning algorithm that balances exploration and exploitation with real-time adaptation to market volatility through policy-based learning.',
    },
    {
      title: translations?.techStack?.components?.featureSelection?.title || 'Feature Selection & Analysis',
      icon: Layers,
      description: translations?.techStack?.components?.featureSelection?.description || 'RandomForest-powered feature importance identifies most predictive market signals with automated dimensionality reduction to eliminate noise.',
    },
    {
      title: translations?.techStack?.components?.parameterOptimization?.title || 'Parameter Optimization',
      icon: Sliders,
      description: translations?.techStack?.components?.parameterOptimization?.description || 'Optuna hyperparameter tuning automatically discovers optimal configuration using Bayesian optimization approach that learns from previous iterations.',
    },
    {
      title: translations?.techStack?.components?.technicalIndicators?.title || 'Technical Indicators Suite',
      icon: ChartLine,
      description: translations?.techStack?.components?.technicalIndicators?.description || 'Enhanced order book analysis, volume profile assessment, multi-timeframe momentum signals, and volatility-adjusted position sizing.',
    },
    {
      title: translations?.techStack?.components?.riskManagement?.title || 'Risk Management Framework',
      icon: Shield,
      description: translations?.techStack?.components?.riskManagement?.description || 'Dynamic trailing stop loss, step profit gathering, drawdown protection mechanisms, and mathematically optimized position sizing algorithm.',
    },
    {
      title: translations?.techStack?.components?.performanceMetrics?.title || 'Performance Metrics',
      icon: BarChart3,
      description: translations?.techStack?.components?.performanceMetrics?.description || 'Risk-adjusted returns, maximum drawdown analysis, win rate & profit factor, and Monte Carlo simulations for stress testing.',
    },
  ];

  return (
    <section ref={ref} className="relative w-full overflow-hidden">
      {/* Background animation/effect */}
      <div className="absolute inset-0 z-0 opacity-20">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 to-blue-900/20"></div>
        <div className="absolute w-full h-full">
          {/* Animated price chart lines in background */}
          <svg width="100%" height="100%" viewBox="0 0 1000 1000" preserveAspectRatio="none">
            <motion.path
              initial={{ pathLength: 0, opacity: 0 }}
              animate={{
                pathLength: 1,
                opacity: 0.2,
                transition: { duration: 3, ease: "easeInOut" }
              }}
              d="M0,500 Q250,400 500,600 T1000,500"
              fill="none"
              stroke="#D946EF"
              strokeWidth="2"
            />
            <motion.path
              initial={{ pathLength: 0, opacity: 0 }}
              animate={{
                pathLength: 1,
                opacity: 0.2,
                transition: { duration: 3, ease: "easeInOut", delay: 0.5 }
              }}
              d="M0,600 Q200,200 400,700 T1000,300"
              fill="none"
              stroke="#3B82F6"
              strokeWidth="2"
            />
          </svg>
        </div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Hero Area */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isIntersecting ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="max-w-4xl mx-auto text-center mb-10"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-500">
            {translations?.title || 'CryptoBot Lab: Reinforcement Learning Meets Market Analysis'}
          </h2>
          <p className="text-gray-300 text-lg mb-6">
            {translations?.subtitle || 'Advanced algorithmic trading powered by machine learning optimization'}
          </p>

          {/* Visual concept - TradingView-like chart with candles */}
          <div className="relative h-96 w-full max-w-3xl mx-auto bg-gray-900/80 rounded-lg overflow-hidden mb-4 border border-gray-800">
            {/* Chart header - like TradingView */}
            <div className="absolute top-0 left-0 right-0 h-8 bg-gray-900 border-b border-gray-800 flex items-center px-3 z-10">
              <div className="text-sm font-medium text-white">BTC/USD</div>
              <div className="text-xs text-gray-400 ml-2">4h</div>
              <div className="ml-2 px-2 py-0.5 bg-gray-800 rounded text-xs text-gray-300">
                <span className="text-green-400">+2.34%</span>
              </div>
              <div className="ml-auto flex space-x-2">
                <div className="w-4 h-4 rounded-sm bg-gray-800 border border-gray-700 flex items-center justify-center">
                  <span className="text-[8px] text-gray-400">D</span>
                </div>
                <div className="w-4 h-4 rounded-sm bg-gray-800 border border-gray-700 flex items-center justify-center">
                  <span className="text-[8px] text-gray-400">I</span>
                </div>
                <div className="w-4 h-4 rounded-sm bg-gray-800 border border-gray-700 flex items-center justify-center">
                  <span className="text-[8px] text-gray-400">⋯</span>
                </div>
              </div>
            </div>

            <div className="absolute inset-0 pt-8 flex items-center justify-center">
              <motion.div
                className="w-full h-full"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 1 }}
              >
                {/* Animated candlestick chart with buy/sell signals */}
                <svg width="100%" height="100%" viewBox="0 0 1000 500" preserveAspectRatio="none">
                  {/* Price axis labels */}
                  <g className="price-axis">
                    <rect x="970" y="0" width="30" height="400" fill="rgba(30, 30, 30, 0.5)" />
                    <text x="985" y="50" textAnchor="end" fill="#999" fontSize="9">42,876.50</text>
                    <text x="985" y="150" textAnchor="end" fill="#999" fontSize="9">41,245.75</text>
                    <text x="985" y="250" textAnchor="end" fill="#999" fontSize="9">39,614.20</text>
                    <text x="985" y="350" textAnchor="end" fill="#999" fontSize="9">37,982.80</text>

                    {/* Current price highlight */}
                    <line x1="0" y1="120" x2="970" y2="120" stroke="#3B82F6" strokeWidth="0.5" strokeDasharray="2,2" />
                    <rect x="970" y="115" width="30" height="10" fill="#3B82F6" />
                    <text x="985" y="123" textAnchor="end" fill="white" fontSize="9" fontWeight="bold">41,582.30</text>
                  </g>

                  {/* Time axis labels */}
                  <g className="time-axis">
                    <rect x="0" y="400" width="970" height="20" fill="rgba(30, 30, 30, 0.5)" />
                    <text x="50" y="415" textAnchor="middle" fill="#999" fontSize="9">Apr 12</text>
                    <text x="150" y="415" textAnchor="middle" fill="#999" fontSize="9">Apr 13</text>
                    <text x="250" y="415" textAnchor="middle" fill="#999" fontSize="9">Apr 14</text>
                    <text x="350" y="415" textAnchor="middle" fill="#999" fontSize="9">Apr 15</text>
                    <text x="450" y="415" textAnchor="middle" fill="#999" fontSize="9">Apr 16</text>
                    <text x="550" y="415" textAnchor="middle" fill="#999" fontSize="9">Apr 17</text>
                    <text x="650" y="415" textAnchor="middle" fill="#999" fontSize="9">Apr 18</text>
                    <text x="750" y="415" textAnchor="middle" fill="#999" fontSize="9">Apr 19</text>
                    <text x="850" y="415" textAnchor="middle" fill="#999" fontSize="9">Apr 20</text>
                    <text x="950" y="415" textAnchor="middle" fill="#999" fontSize="9">Apr 21</text>
                  </g>

                  {/* Background grid lines */}
                  <g stroke="#333" strokeWidth="0.3">
                    <line x1="0" y1="50" x2="970" y2="50" strokeDasharray="1,3" />
                    <line x1="0" y1="100" x2="970" y2="100" strokeDasharray="1,3" />
                    <line x1="0" y1="150" x2="970" y2="150" strokeDasharray="1,3" />
                    <line x1="0" y1="200" x2="970" y2="200" strokeDasharray="1,3" />
                    <line x1="0" y1="250" x2="970" y2="250" strokeDasharray="1,3" />
                    <line x1="0" y1="300" x2="970" y2="300" strokeDasharray="1,3" />
                    <line x1="0" y1="350" x2="970" y2="350" strokeDasharray="1,3" />

                    <line x1="50" y1="0" x2="50" y2="400" strokeDasharray="1,3" />
                    <line x1="150" y1="0" x2="150" y2="400" strokeDasharray="1,3" />
                    <line x1="250" y1="0" x2="250" y2="400" strokeDasharray="1,3" />
                    <line x1="350" y1="0" x2="350" y2="400" strokeDasharray="1,3" />
                    <line x1="450" y1="0" x2="450" y2="400" strokeDasharray="1,3" />
                    <line x1="550" y1="0" x2="550" y2="400" strokeDasharray="1,3" />
                    <line x1="650" y1="0" x2="650" y2="400" strokeDasharray="1,3" />
                    <line x1="750" y1="0" x2="750" y2="400" strokeDasharray="1,3" />
                    <line x1="850" y1="0" x2="850" y2="400" strokeDasharray="1,3" />
                    <line x1="950" y1="0" x2="950" y2="400" strokeDasharray="1,3" />
                  </g>

                  {/* Moving Average Line */}
                  <motion.path
                    initial={{ pathLength: 0, opacity: 0 }}
                    animate={{
                      pathLength: 1,
                      opacity: 0.6,
                      transition: { duration: 2, ease: "easeInOut", delay: 3.0 }
                    }}
                    d="M50,180 C150,170 250,190 350,160 C450,140 550,170 650,150 C750,130 850,140 950,120"
                    fill="none"
                    stroke="#3B82F6"
                    strokeWidth="1.5"
                  />

                  {/* Candlesticks - more TradingView-like */}
                  {/* Candle 1 - Bullish */}
                  <motion.g
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3, duration: 0.3 }}
                  >
                    <line x1="50" y1="150" x2="50" y2="250" stroke="#10B981" strokeWidth="1" />
                    <rect x="45" y="180" width="10" height="70" fill="#10B981" fillOpacity="0.8" />
                  </motion.g>

                  {/* Candle 2 - Bearish */}
                  <motion.g
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4, duration: 0.3 }}
                  >
                    <line x1="100" y1="120" x2="100" y2="220" stroke="#EF4444" strokeWidth="1" />
                    <rect x="95" y="120" width="10" height="60" fill="#EF4444" fillOpacity="0.8" />
                  </motion.g>

                  {/* Candle 3 - Bearish */}
                  <motion.g
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5, duration: 0.3 }}
                  >
                    <line x1="150" y1="100" x2="150" y2="240" stroke="#EF4444" strokeWidth="1" />
                    <rect x="145" y="100" width="10" height="80" fill="#EF4444" fillOpacity="0.8" />
                  </motion.g>

                  {/* Candle 4 - Bearish */}
                  <motion.g
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6, duration: 0.3 }}
                  >
                    <line x1="200" y1="90" x2="200" y2="190" stroke="#EF4444" strokeWidth="1" />
                    <rect x="195" y="90" width="10" height="60" fill="#EF4444" fillOpacity="0.8" />
                  </motion.g>

                  {/* Candle 5 - Doji */}
                  <motion.g
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.7, duration: 0.3 }}
                  >
                    <line x1="250" y1="120" x2="250" y2="200" stroke="#888" strokeWidth="1" />
                    <rect x="245" y="158" width="10" height="4" fill="#888" fillOpacity="0.8" />
                  </motion.g>

                  {/* Candle 6 - Bullish */}
                  <motion.g
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8, duration: 0.3 }}
                  >
                    <line x1="300" y1="140" x2="300" y2="220" stroke="#10B981" strokeWidth="1" />
                    <rect x="295" y="170" width="10" height="50" fill="#10B981" fillOpacity="0.8" />
                  </motion.g>

                  {/* Candle 7 - Bullish */}
                  <motion.g
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.9, duration: 0.3 }}
                  >
                    <line x1="350" y1="120" x2="350" y2="220" stroke="#10B981" strokeWidth="1" />
                    <rect x="345" y="160" width="10" height="60" fill="#10B981" fillOpacity="0.8" />
                  </motion.g>

                  {/* Candle 8 - Bullish */}
                  <motion.g
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.0, duration: 0.3 }}
                  >
                    <line x1="400" y1="100" x2="400" y2="200" stroke="#10B981" strokeWidth="1" />
                    <rect x="395" y="140" width="10" height="60" fill="#10B981" fillOpacity="0.8" />
                  </motion.g>

                  {/* Candle 9 - Bullish */}
                  <motion.g
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.1, duration: 0.3 }}
                  >
                    <line x1="450" y1="80" x2="450" y2="180" stroke="#10B981" strokeWidth="1" />
                    <rect x="445" y="120" width="10" height="60" fill="#10B981" fillOpacity="0.8" />
                  </motion.g>

                  {/* Candle 10 - Bearish */}
                  <motion.g
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.2, duration: 0.3 }}
                  >
                    <line x1="500" y1="60" x2="500" y2="180" stroke="#EF4444" strokeWidth="1" />
                    <rect x="495" y="60" width="10" height="70" fill="#EF4444" fillOpacity="0.8" />
                  </motion.g>

                  {/* Candle 11 - Bearish */}
                  <motion.g
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.3, duration: 0.3 }}
                  >
                    <line x1="550" y1="80" x2="550" y2="200" stroke="#EF4444" strokeWidth="1" />
                    <rect x="545" y="80" width="10" height="60" fill="#EF4444" fillOpacity="0.8" />
                  </motion.g>

                  {/* Candle 12 - Doji */}
                  <motion.g
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.4, duration: 0.3 }}
                  >
                    <line x1="600" y1="100" x2="600" y2="180" stroke="#888" strokeWidth="1" />
                    <rect x="595" y="138" width="10" height="4" fill="#888" fillOpacity="0.8" />
                  </motion.g>

                  {/* Candle 13 - Bullish */}
                  <motion.g
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.5, duration: 0.3 }}
                  >
                    <line x1="650" y1="110" x2="650" y2="190" stroke="#10B981" strokeWidth="1" />
                    <rect x="645" y="150" width="10" height="40" fill="#10B981" fillOpacity="0.8" />
                  </motion.g>

                  {/* Candle 14 - Bullish */}
                  <motion.g
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.6, duration: 0.3 }}
                  >
                    <line x1="700" y1="90" x2="700" y2="170" stroke="#10B981" strokeWidth="1" />
                    <rect x="695" y="130" width="10" height="40" fill="#10B981" fillOpacity="0.8" />
                  </motion.g>

                  {/* Candle 15 - Bullish */}
                  <motion.g
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.7, duration: 0.3 }}
                  >
                    <line x1="750" y1="70" x2="750" y2="150" stroke="#10B981" strokeWidth="1" />
                    <rect x="745" y="110" width="10" height="40" fill="#10B981" fillOpacity="0.8" />
                  </motion.g>

                  {/* Candle 16 - Bearish */}
                  <motion.g
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.8, duration: 0.3 }}
                  >
                    <line x1="800" y1="50" x2="800" y2="150" stroke="#EF4444" strokeWidth="1" />
                    <rect x="795" y="50" width="10" height="60" fill="#EF4444" fillOpacity="0.8" />
                  </motion.g>

                  {/* Candle 17 - Bearish */}
                  <motion.g
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.9, duration: 0.3 }}
                  >
                    <line x1="850" y1="70" x2="850" y2="170" stroke="#EF4444" strokeWidth="1" />
                    <rect x="845" y="70" width="10" height="50" fill="#EF4444" fillOpacity="0.8" />
                  </motion.g>

                  {/* Candle 18 - Bullish */}
                  <motion.g
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 2.0, duration: 0.3 }}
                  >
                    <line x1="900" y1="90" x2="900" y2="170" stroke="#10B981" strokeWidth="1" />
                    <rect x="895" y="130" width="10" height="40" fill="#10B981" fillOpacity="0.8" />
                  </motion.g>

                  {/* Candle 19 - Bullish */}
                  <motion.g
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 2.1, duration: 0.3 }}
                  >
                    <line x1="950" y1="70" x2="950" y2="150" stroke="#10B981" strokeWidth="1" />
                    <rect x="945" y="110" width="10" height="40" fill="#10B981" fillOpacity="0.8" />
                  </motion.g>

                  {/* Volume bars */}
                  <g transform="translate(0, 430)">
                    {/* Volume label */}
                    <text x="10" y="20" fill="#999" fontSize="10">Volume</text>

                    {/* Volume bars */}
                    <motion.g initial={{ opacity: 0 }} animate={{ opacity: 0.7 }} transition={{ delay: 2.3, duration: 0.5 }}>
                      <rect x="45" y="-30" width="10" height="30" fill="#10B981" fillOpacity="0.5" />
                      <rect x="95" y="-40" width="10" height="40" fill="#EF4444" fillOpacity="0.5" />
                      <rect x="145" y="-50" width="10" height="50" fill="#EF4444" fillOpacity="0.5" />
                      <rect x="195" y="-35" width="10" height="35" fill="#EF4444" fillOpacity="0.5" />
                      <rect x="245" y="-15" width="10" height="15" fill="#888" fillOpacity="0.5" />
                      <rect x="295" y="-25" width="10" height="25" fill="#10B981" fillOpacity="0.5" />
                      <rect x="345" y="-30" width="10" height="30" fill="#10B981" fillOpacity="0.5" />
                      <rect x="395" y="-45" width="10" height="45" fill="#10B981" fillOpacity="0.5" />
                      <rect x="445" y="-55" width="10" height="55" fill="#10B981" fillOpacity="0.5" />
                      <rect x="495" y="-60" width="10" height="60" fill="#EF4444" fillOpacity="0.5" />
                      <rect x="545" y="-40" width="10" height="40" fill="#EF4444" fillOpacity="0.5" />
                      <rect x="595" y="-20" width="10" height="20" fill="#888" fillOpacity="0.5" />
                      <rect x="645" y="-25" width="10" height="25" fill="#10B981" fillOpacity="0.5" />
                      <rect x="695" y="-30" width="10" height="30" fill="#10B981" fillOpacity="0.5" />
                      <rect x="745" y="-35" width="10" height="35" fill="#10B981" fillOpacity="0.5" />
                      <rect x="795" y="-65" width="10" height="65" fill="#EF4444" fillOpacity="0.5" />
                      <rect x="845" y="-45" width="10" height="45" fill="#EF4444" fillOpacity="0.5" />
                      <rect x="895" y="-30" width="10" height="30" fill="#10B981" fillOpacity="0.5" />
                      <rect x="945" y="-40" width="10" height="40" fill="#10B981" fillOpacity="0.5" />
                    </motion.g>
                  </g>

                  {/* Buy signals - more TradingView-like */}
                  <motion.g
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 2.5, duration: 0.3 }}
                  >
                    <path d="M450,190 L450,210 L440,200 Z" fill="#10B981" />
                  </motion.g>

                  <motion.g
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 2.6, duration: 0.3 }}
                  >
                    <path d="M750,160 L750,180 L740,170 Z" fill="#10B981" />
                  </motion.g>

                  <motion.g
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 2.7, duration: 0.3 }}
                  >
                    <path d="M950,160 L950,180 L940,170 Z" fill="#10B981" />
                  </motion.g>

                  {/* Sell signals - more TradingView-like */}
                  <motion.g
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 2.8, duration: 0.3 }}
                  >
                    <path d="M500,50 L500,30 L510,40 Z" fill="#EF4444" />
                  </motion.g>

                  <motion.g
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 2.9, duration: 0.3 }}
                  >
                    <path d="M800,40 L800,20 L810,30 Z" fill="#EF4444" />
                  </motion.g>

                  {/* Annotations - like TradingView */}
                  <motion.g
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 3.2, duration: 0.5 }}
                  >
                    {/* Support line */}
                    <line x1="400" y1="200" x2="950" y2="200" stroke="#3B82F6" strokeWidth="1" strokeDasharray="5,5" />
                    <text x="960" y="200" fill="#3B82F6" fontSize="10" dominantBaseline="middle">Support</text>

                    {/* Resistance line */}
                    <line x1="400" y1="80" x2="950" y2="80" stroke="#EF4444" strokeWidth="1" strokeDasharray="5,5" />
                    <text x="960" y="80" fill="#EF4444" fontSize="10" dominantBaseline="middle">Resistance</text>
                  </motion.g>
                </svg>
              </motion.div>
            </div>
          </div>
        </motion.div>

        {/* Overview Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isIntersecting ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="max-w-4xl mx-auto mb-12 bg-gray-900/40 backdrop-blur-sm rounded-lg p-8"
        >
          <h4 className="text-xl font-bold text-cyan-400 mb-4">
            {translations?.overview?.title || 'Beyond Traditional Trading Algorithms'}
          </h4>
          <p className="text-gray-300 text-lg">
            {translations?.overview?.description || 'Our CryptoBot combines reinforcement learning with traditional technical analysis to create an adaptive trading system that evolves with market conditions. Unlike static rule-based systems, our approach continuously optimizes performance while managing risk through mathematical precision.'}
          </p>
        </motion.div>

        {/* Core Technology Stack */}
        <div className="mb-12">
          <motion.h3
            initial={{ opacity: 0, y: 20 }}
            animate={isIntersecting ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-2xl font-bold text-center text-white mb-8"
          >
            {translations?.techStack?.title || 'Core Technology Stack'}
          </motion.h3>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={isIntersecting ? "show" : "hidden"}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {techStackComponents.map((component, index) => (
              <ServiceCard
                key={index}
                title={component.title}
                lucideIcon={component.icon}
                description={component.description}
              />
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
}
