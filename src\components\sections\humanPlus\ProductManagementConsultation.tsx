'use client';

import React from 'react';
import { motion } from 'framer-motion';
import useIntersectionObserver from '@/hooks/useIntersectionObserver';
import ProductManagementCard from './ProductManagementCard';

interface ProductManagementConsultationProps {
  translations: {
    title: string;
    subtitle: string;
    introduction?: string;
    cta?: string;
    tabs: {
      overview: string;
      services: string;
      approach: string;
      casestudies: string;
    };
    cards: {
      strategy: {
        title: string;
        description: string;
        features: string[];
        cta: string;
      };
      agile: {
        title: string;
        description: string;
        features: string[];
        cta: string;
      };
      leadership: {
        title: string;
        description: string;
        features: string[];
        cta: string;
      };
      design: {
        title: string;
        description: string;
        features: string[];
        cta: string;
      };
      analytics: {
        title: string;
        description: string;
        features: string[];
        cta: string;
      };
    };
    contact?: {
      title: string;
      description: string;
      primaryCta: string;
      secondaryCta: string;
    };
  };
}

export default function ProductManagementConsultation({ translations }: ProductManagementConsultationProps) {
  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 0.1 });

  // Define card icons and colors
  const cardDetails = {
    strategy: {
      icon: '🧭',
      color: '#3366CC',
      bgGradient: 'from-blue-600/20 to-blue-800/20',
    },
    agile: {
      icon: '🔄',
      color: '#3366CC',
      bgGradient: 'from-blue-600/20 to-blue-800/20',
    },
    leadership: {
      icon: '💡',
      color: '#3366CC',
      bgGradient: 'from-blue-600/20 to-blue-800/20',
    },
    design: {
      icon: '👤',
      color: '#3366CC',
      bgGradient: 'from-blue-600/20 to-blue-800/20',
    },
    analytics: {
      icon: '📊',
      color: '#3366CC',
      bgGradient: 'from-blue-600/20 to-blue-800/20',
    },
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  return (
    <div ref={ref} className="relative min-h-screen bg-space-black pt-32 pb-20">
      {/* Background with neural network aesthetic */}
      <div className="absolute inset-0 z-0 opacity-20">
        <div className="absolute inset-0 bg-gradient-to-b from-purple-900/30 to-transparent"></div>
        <div className="h-full w-full bg-[url('/images/future-tech-grid.svg')] bg-cover bg-center"></div>
      </div>

      {/* Connector to the next section */}
      <div className="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-black to-transparent z-10"></div>

      {/* Title Section */}
      <section className="relative mb-16">
        <div className="container relative z-10 mx-auto px-4">
          <motion.h1
            className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-center bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-purple-500 to-indigo-600"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {translations.title}
          </motion.h1>

          <motion.p
            className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto text-center mb-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {translations.subtitle}
          </motion.p>

          {translations.introduction && (
            <motion.p
              className="text-lg text-gray-400 max-w-3xl mx-auto text-center mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              {translations.introduction}
            </motion.p>
          )}

          {translations.cta && (
            <motion.div
              className="flex justify-center mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <button className="px-8 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-medium rounded-full hover:from-blue-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                {translations.cta}
              </button>
            </motion.div>
          )}
        </div>
      </section>

      {/* Product Management Cards Section */}
      <section className="mb-12">
        <div className="container mx-auto px-4">
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            variants={containerVariants}
            initial="hidden"
            animate={isIntersecting ? "visible" : "hidden"}
          >
            <ProductManagementCard
              title={translations.cards.strategy.title}
              description={translations.cards.strategy.description}
              features={translations.cards.strategy.features}
              cta={translations.cards.strategy.cta}
              icon={cardDetails.strategy.icon}
              color={cardDetails.strategy.color}
              bgGradient={cardDetails.strategy.bgGradient}
              variants={itemVariants}
            />
            <ProductManagementCard
              title={translations.cards.agile.title}
              description={translations.cards.agile.description}
              features={translations.cards.agile.features}
              cta={translations.cards.agile.cta}
              icon={cardDetails.agile.icon}
              color={cardDetails.agile.color}
              bgGradient={cardDetails.agile.bgGradient}
              variants={itemVariants}
            />
            <ProductManagementCard
              title={translations.cards.leadership.title}
              description={translations.cards.leadership.description}
              features={translations.cards.leadership.features}
              cta={translations.cards.leadership.cta}
              icon={cardDetails.leadership.icon}
              color={cardDetails.leadership.color}
              bgGradient={cardDetails.leadership.bgGradient}
              variants={itemVariants}
            />
            <ProductManagementCard
              title={translations.cards.design.title}
              description={translations.cards.design.description}
              features={translations.cards.design.features}
              cta={translations.cards.design.cta}
              icon={cardDetails.design.icon}
              color={cardDetails.design.color}
              bgGradient={cardDetails.design.bgGradient}
              variants={itemVariants}
            />
            <ProductManagementCard
              title={translations.cards.analytics.title}
              description={translations.cards.analytics.description}
              features={translations.cards.analytics.features}
              cta={translations.cards.analytics.cta}
              icon={cardDetails.analytics.icon}
              color={cardDetails.analytics.color}
              bgGradient={cardDetails.analytics.bgGradient}
              variants={itemVariants}
            />
          </motion.div>
        </div>
      </section>
    </div>
  );
}
