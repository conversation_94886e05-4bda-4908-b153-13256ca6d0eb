'use client';

import { useState, useEffect, useRef, ReactNode } from 'react';
import { motion } from 'framer-motion';
import CIOSection from '@/components/sections/services/CIOSection';
import MLSection from '@/components/sections/services/MLSection';
import ITSection from '@/components/sections/services/ITSection';
import ConsultingSection from '@/components/sections/services/ConsultingSection';
import InsuranceSection from '@/components/sections/services/InsuranceSection';

// Component that resets scroll position when it mounts
const ScrollResetter = ({ children }: { children: ReactNode }) => {
  // This component will be remounted whenever the key changes
  // When it mounts, it will be at the top of its container
  return <div className="scroll-resetter">{children}</div>;
};

type Section = {
  id: string;
  title: string;
};

type ServicesPageProps = {
  translations: {
    title: string;
    pageTitle: string;
    pageSubtitle: string;
    sections: Section[];
    insurance?: {
      pageTitle: string;
      pageDescription: string;
      services: Array<{ title: string; icon: string; description: string; }>;
      philosophy: {
        title: string;
        description: string;
        pillars: Array<{ title: string; description: string; }>;
      };
    };
    ml?: {
      pageTitle: string;
      pageDescription: string;
      services: Array<{ title: string; icon: string; description: string; }>;
      mlArchitectureDescription: string;
      process: {
        title: string;
        steps: Array<{ number: string; title: string; description: string; }>;
      };
    };
    it?: {
      pageTitle: string;
      pageDescription: string;
      services: Array<{ title: string; icon: string; description: string; }>;
      philosophy: {
        title: string;
        description: string;
        pillars: Array<{ title: string; description: string; }>;
      };
    };
    consulting?: {
      pageTitle: string;
      pageDescription: string;
      services: Array<{ title: string; icon: string; description: string; }>;
      methodology: {
        title: string;
        description: string;
        phases: Array<{ title: string; description: string; }>;
      };
    };
    cio?: {
      pageTitle: string;
      pageDescription: string;
      services: Array<{ title: string; icon: string; description: string; }>;
      approach: {
        title: string;
        description: string;
        pillars: Array<{ title: string; description: string; }>;
      };
    };
  };
};

export default function ServicesPage({ translations }: ServicesPageProps) {
  const [activeSection, setActiveSection] = useState<string>('insurance');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const contentSectionRef = useRef<HTMLDivElement>(null);

  // Check localStorage for active section on component mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedSection = localStorage.getItem('activeServiceSection');
      if (storedSection) {
        console.log('Found stored section:', storedSection);
        setActiveSection(storedSection);
        // Clear the stored section to avoid it persisting across page refreshes
        localStorage.removeItem('activeServiceSection');
      }
    }
  }, []);

  // Function to handle section change
  const handleSectionChange = (sectionId: string) => {
    // Set the new active section
    setActiveSection(sectionId);

    // After the section changes, scroll the content section to top (but keep nav visible)
    setTimeout(() => {
      if (contentSectionRef.current) {
        // Only scroll if the nav is visible (i.e., user has scrolled past header)
        const nav = document.getElementById('service-navigation');
        if (nav) {
          const navRect = nav.getBoundingClientRect();
          const navStickyTop = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--nav-sticky-top') || '0',10);
          // If nav is at or near the top of the viewport, scroll content just below it
          if (navRect.top >= 10 && navRect.bottom > 0) {
            // Scroll the content section so its top aligns just below the nav
            const contentRect = contentSectionRef.current.getBoundingClientRect();
            const scrollY = window.scrollY + contentRect.top - navRect.height - navStickyTop;
            window.scrollTo({ top: scrollY, behavior: 'instant' in window ? 'instant' : 'auto' });
          }
        }
      }
    }, 0);
  };

  // Log when active section changes
  useEffect(() => {
    console.log('Active section is now:', activeSection);
  }, [activeSection]);

  // Render the active section based on state
  const renderActiveSection = () => {
    switch (activeSection) {
      case 'insurance':
        return translations.insurance ? <InsuranceSection translations={translations.insurance} /> : null;
      case 'cio':
        return translations.cio ? <CIOSection translations={translations.cio} /> : null;
      case 'ml':
        return translations.ml ? <MLSection translations={translations.ml} /> : null;
      case 'it':
        return translations.it ? <ITSection translations={translations.it} /> : null;
      case 'consulting':
        return translations.consulting ? <ConsultingSection translations={translations.consulting} /> : null;
      default:
        return translations.insurance ? <InsuranceSection translations={translations.insurance} /> : null;
    }
  };

  // Navigation component (previously separate)
  const ServiceNavigation = () => (
    <div
      id="service-navigation"
      className="sticky w-full left-0 right-0 bg-black/90 backdrop-blur-lg py-3 border-b border-gray-800"
      style={{
        position: 'sticky',
        top: 'var(--nav-sticky-top)',
        zIndex: 'var(--service-nav-z-index)'
      }}
    >
      <div className="container mx-auto px-4">
        {/* Mobile dropdown for smaller screens */}
        <div className="block md:hidden">
          <div
            className="flex items-center justify-between py-2"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <span className="text-cyan-400 font-medium">
              {translations.sections.find(s => s.id === activeSection)?.title || 'Select Service'}
            </span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`h-5 w-5 text-cyan-400 transition-transform ${isMobileMenuOpen ? 'transform rotate-180' : ''}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>

          {/* Mobile dropdown menu */}
          {isMobileMenuOpen && (
            <div className="absolute left-0 right-0 mt-1 py-2 px-4 bg-gray-900/95 backdrop-blur-lg shadow-lg z-50">
              {translations.sections.map((section) => (
                <div
                  key={section.id}
                  className="relative"
                  onClick={() => {
                    handleSectionChange(section.id);
                    setIsMobileMenuOpen(false);
                  }}
                >
                  <div
                    className={`px-4 py-3 text-base font-medium transition-all duration-300 cursor-pointer ${
                      activeSection === section.id
                        ? 'text-cyan-400'
                        : 'text-gray-400 hover:text-white'
                    }`}
                  >
                    {section.title}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Desktop navigation */}
        <div className="hidden md:block">
          <div className="flex flex-wrap justify-center gap-2 md:gap-8">
            {translations.sections.map((section) => (
              <div
                key={section.id}
                className="relative"
              >
                <div
                  onClick={() => {
                    console.log('Clicked on section:', section.id);
                    handleSectionChange(section.id);
                  }}
                  className={`px-4 py-2 text-button font-medium transition-all duration-300 cursor-pointer ${
                    activeSection === section.id
                      ? 'text-cyan-400'
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  {section.title}
                </div>
                {activeSection === section.id && (
                  <motion.div
                    className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-cyan-400 to-purple-500"
                    layoutId="activeSection"
                    transition={{ type: 'spring', stiffness: 380, damping: 30 }}
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <>
      {/* Header section with title and description */}
      <section className="relative bg-black pt-32 pb-8 services-page">
        <div className="container mx-auto px-4">
          <motion.h2
            className="text-3xl md:text-4xl font-bold mb-6 text-center bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-500"
            initial={{ opacity: 0, y: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            {translations.pageTitle}
          </motion.h2>

          <motion.p
            className="text-lg text-gray-300 max-w-3xl mx-auto text-center mb-4"
            initial={{ opacity: 0, y: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {translations.pageSubtitle}
          </motion.p>
        </div>
      </section>

      {/* Integrated navigation section */}
      <ServiceNavigation />

      {/* Content section */}
      <section className="relative bg-black pb-20">
        <div className="container mx-auto px-4" style={{ zIndex: 'var(--content-z-index)' }}>
          {/* Active service section - using key to force remount when section changes */}
          <div id="service-content" className="w-full pt-8" ref={contentSectionRef}>
            {/* Using key to force the component to remount when active section changes */}
            <ScrollResetter key={activeSection}>
              {renderActiveSection()}
            </ScrollResetter>
          </div>
        </div>
      </section>
    </>
  );
}
