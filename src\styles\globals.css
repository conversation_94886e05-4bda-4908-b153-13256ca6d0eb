@import "tailwindcss";

/* Root variables */
:root {
  /* Colors */
  --color-background: #050510;
  --color-foreground: #f8f8ff;
  --color-primary: #00FFFF;
  --color-secondary: #FF00FF;
  --color-accent: #7B00FF;
  --color-neutral: #1A1A2E;

  /* Layout */
  --header-height: 40px; /* Default value, will be updated by JS */
  --nav-sticky-top: 40px; /* Default value for sticky navigation - match header height */
  --header-z-index: 50; /* Z-index for header */
  --service-nav-z-index: 40; /* Z-index for service navigation (below header) */
  --content-z-index: 30; /* Z-index for content (below navigation) */

  /* Neural network color palette */
  --neural-gradient-start: #0A0A20;
  --neural-gradient-end: #1A1A40;
  --neural-node-color: #00E5FF;
  --neural-connection-color: #7B00FF;
  --neural-pulse-color: rgba(0, 255, 255, 0.4);

  /* Typography */
  --font-family-base: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  --font-family-display: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;

  /* Spacing */
  --spacing-base: 4px;
  --section-spacing: 120px;

  /* Animation */
  --animation-speed-slow: 1000ms;
  --animation-speed-medium: 500ms;
  --animation-speed-fast: 300ms;

  /* Borders & Shadows */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 16px;
  --border-radius-xl: 24px;
  --border-radius-full: 9999px;

  --glow-sm: 0 0 5px var(--neural-pulse-color);
  --glow-md: 0 0 15px var(--neural-pulse-color);
  --glow-lg: 0 0 30px var(--neural-pulse-color);
}

/* Custom fonts - Import your fonts here */
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* Base styles */
html {
  scroll-behavior: smooth;
}

body {
  background-color: var(--color-background);
  color: var(--color-foreground);
  font-family: var(--font-family-base);
  overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-display);
  font-weight: 700;
  letter-spacing: -0.025em;
}

h1 {
  font-size: 2.25rem;
  line-height: 2.5rem;
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (min-width: 768px) {
  h1 {
    font-size: 3rem;
    line-height: 1;
  }
}

@media (min-width: 1024px) {
  h1 {
    font-size: 3.75rem;
    line-height: 1;
  }
}

h2 {
  font-size: 1.875rem;
  line-height: 2.25rem;
  margin-bottom: calc(var(--spacing-base) * 6);
}

@media (min-width: 768px) {
  h2 {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

h3 {
  font-size: 1.5rem;
  line-height: 2rem;
}

@media (min-width: 768px) {
  h3 {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}

h4 {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

@media (min-width: 768px) {
  h4 {
    font-size: 1.5rem;
    line-height: 2rem;
  }
}

h5 {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

@media (min-width: 768px) {
  h5 {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

h6 {
  font-size: 1rem;
  line-height: 1.5rem;
}

@media (min-width: 768px) {
  h6 {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

/* Common Components */
.container {
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
  max-width: 1280px;
}

@media (min-width: 768px) {
  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.section {
  padding-top: 4rem;
  padding-bottom: 4rem;
  margin-bottom: var(--section-spacing);
}

@media (min-width: 768px) {
  .section {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
}

/* Header safe area - use this for sections that need to account for the header */
.header-safe-area {
  padding-top: var(--header-height);
}

/* Content safe area - use this for content that needs to be visible below the header */
.content-safe-area {
  margin-top: var(--header-height);
}

/* Page overlay for consistent backdrop blur */
.page-backdrop-blur {
  position: fixed;
  inset: 0;
  z-index: 40;
  backdrop-filter: blur(12px);
  background-color: #000000;
  opacity: 0.4;
  pointer-events: none; /* Allow clicks to pass through */
}

/* Glowing elements */
.glow-text {
  text-shadow: 0 0 10px var(--neural-pulse-color);
}

.glow-border {
  border: 1px solid var(--color-primary);
  box-shadow: var(--glow-md);
}

.neural-gradient {
  background: linear-gradient(135deg, var(--neural-gradient-start), var(--neural-gradient-end));
}

/* Sticky Service Navigation styles for all screen sizes */
#sticky-service-navigation {
  position: sticky !important; /* Force sticky positioning */
  top: var(--nav-sticky-top) !important; /* Use CSS variable for consistent positioning */
  z-index: var(--service-nav-z-index) !important; /* Force z-index */
  background-color: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(12px);
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(75, 85, 99, 0.5);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  width: 100%;
  left: 0;
  right: 0;
  display: block !important; /* Ensure it's always displayed */
  visibility: visible !important; /* Ensure it's always visible */
  opacity: 1 !important; /* Ensure it's fully opaque */
}

/* Sticky Labs Header styles for all screen sizes */
#labs-header {
  position: sticky !important; /* Force sticky positioning */
  top: var(--nav-sticky-top) !important; /* Use CSS variable for consistent positioning */
  z-index: var(--service-nav-z-index) !important; /* Force z-index */
  background-color: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(12px);
  transition: all 0.3s ease;
  width: 100%;
  left: 0;
  right: 0;
  display: block !important; /* Ensure it's always displayed */
  visibility: visible !important; /* Ensure it's always visible */
  opacity: 1 !important; /* Ensure it's fully opaque */
}

/* Services page specific styles */
.services-page {
  /* Ensure proper stacking context for sticky elements */
  position: relative;
  z-index: 1;
}

/* ML Architecture page specific styles */
.ml-architecture-page {
  /* Ensure proper stacking context for sticky elements */
  position: relative;
  z-index: 1;
}

/* Products page specific styles */
.products-page {
  /* Ensure proper stacking context for sticky elements */
  position: relative;
  z-index: 1;
}

/* Product Management page specific styles */
.product-management-page {
  /* Ensure proper stacking context for sticky elements */
  position: relative;
  z-index: 1;
}

/* Ensure content scrolls properly under sticky elements */
#service-content, #ml-architecture-content, #product-content, #product-management-content, #labs-content {
  position: relative;
  z-index: var(--content-z-index); /* Use CSS variable for consistency */
}

/* Dark cyberpunk aesthetic */
.cyber-panel {
  backdrop-filter: blur(16px);
  border: 1px solid;
  border-radius: 0.5rem;
  padding: 1.5rem;
  background-color: rgba(10, 10, 20, 0.5); /* custom dark translucent background */
  border-color: var(--color-neutral);
}

.cyber-button {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  border-radius: 0.375rem;
  position: relative;
  overflow: hidden;
  transition-property: all;
  transition-duration: 300ms;
  font-weight: 500;
  background-color: transparent;
  border: 1px solid var(--color-primary);
  color: var(--color-primary);
  box-shadow: var(--glow-sm);
}

.cyber-button:hover {
  box-shadow: var(--glow-md);
  transform: translateY(-2px);
}

.cyber-button:active {
  transform: translateY(0);
}

.cyber-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 255, 255, 0.2),
    transparent
  );
  transition: left 0.7s;
}

.cyber-button:hover::before {
  left: 100%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  :root {
    --section-spacing: 80px;
    --header-height: 36px; /* Even smaller header height for mobile */
    --nav-sticky-top: 36px; /* Match the mobile header height exactly */
  }

  .section {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  /* Services, ML Architecture, Products, Product Management, and Labs page specific mobile adjustments */
  #service-content, #ml-architecture-content, #product-content, #product-management-content, #labs-content {
    padding-top: 1rem;
    /* Ensure content is visible on mobile */
    visibility: visible !important;
    opacity: 1 !important;
    display: block !important;
  }

  /* Force all motion divs to be visible on mobile */
  .motion-div, motion.div, motion.p, motion.section {
    visibility: visible !important;
    opacity: 1 !important;
    display: block !important;
  }

  /* Ensure all sticky elements work properly on mobile */
  .sticky, [class*="sticky"] {
    position: sticky !important;
    top: var(--nav-sticky-top) !important; /* Use CSS variable for consistency */
  }

  /* Mobile-specific Service Navigation adjustments */
  #sticky-service-navigation {
    top: var(--nav-sticky-top) !important; /* Use CSS variable for consistency */
    position: sticky !important; /* Force sticky positioning on mobile */
    width: 100% !important;
    z-index: var(--service-nav-z-index) !important; /* Force z-index */
    visibility: visible !important; /* Ensure it's always visible */
    opacity: 1 !important; /* Ensure it's fully opaque */
    display: block !important; /* Ensure it's always displayed */
  }

  /* Mobile-specific Labs Header adjustments */
  #labs-header {
    top: var(--nav-sticky-top) !important; /* Use CSS variable for consistency */
    position: sticky !important; /* Force sticky positioning on mobile */
    width: 100% !important;
    z-index: var(--service-nav-z-index) !important; /* Force z-index */
    visibility: visible !important; /* Ensure it's always visible */
    opacity: 1 !important; /* Ensure it's fully opaque */
    display: block !important; /* Ensure it's always displayed */
  }

  /* Ensure services, ML Architecture, Products, Product Management, and Labs page layout works on mobile */
  .services-page, .ml-architecture-page, .products-page, .product-management-page, .labs-page {
    padding-top: calc(var(--header-height) + 1rem) !important; /* Adjust top padding for mobile */
  }

  /* Ensure the mobile dropdown menu is properly positioned */
  #service-navigation .absolute, #ml-architecture-navigation .absolute, #product-navigation .absolute, #product-management-navigation .absolute {
    top: 100%;
    left: 0;
    right: 0;
    z-index: 50;
  }
}

/* Animation keyframes */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Animation classes */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 4s ease-in-out infinite;
}

.animate-rotate {
  animation: rotate 10s linear infinite;
}

@keyframes rotate-360 {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.hover\:rotate-360:hover {
  animation: rotate-360 0.6s ease-in-out;
}

/* Custom Scrollbar Styling */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: var(--neural-gradient-start);
  border-radius: var(--border-radius-md);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, var(--color-primary), var(--color-accent));
  border-radius: var(--border-radius-md);
  border: 2px solid var(--neural-gradient-start);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, var(--color-primary), var(--color-secondary));
  box-shadow: var(--glow-sm);
}

/* Firefox scrollbar styling */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--color-primary) var(--neural-gradient-start);
}
