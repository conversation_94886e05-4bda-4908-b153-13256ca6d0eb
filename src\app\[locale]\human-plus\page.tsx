// src/app/[locale]/human-plus/page.tsx
import { getServerTranslations } from '@/lib/i18n';
import HumanPlusPageContent from '@/components/sections/humanPlus/HumanPlusPageContent';

export default async function HumanPlusPage({ params }: { params: { locale: string } | Promise<{ locale: string }> }) {
  // Handle both Promise and direct object cases
  const resolvedParams = params instanceof Promise ? await params : params

  // Get translations on the server
  const { t } = await getServerTranslations(resolvedParams.locale, ['language']);

  // Prepare translations for ProductManagementSection
  const productManagementSectionTranslations = {
    title: t('humanPlus.productManagementSection.title'),
    subtitle: t('humanPlus.productManagementSection.subtitle'),
    exploreButton: t('humanPlus.productManagementSection.exploreButton'),
    managementPhases: {
      productDevelopment: {
        title: t('humanPlus.productManagementSection.managementPhases.productDevelopment.title'),
        description: t('humanPlus.productManagementSection.managementPhases.productDevelopment.description'),
        processes: {
          productFeasibility: t('humanPlus.productManagementSection.managementPhases.productDevelopment.processes.productFeasibility'),
          developmentMedical: t('humanPlus.productManagementSection.managementPhases.productDevelopment.processes.developmentMedical'),
          rAndD: t('humanPlus.productManagementSection.managementPhases.productDevelopment.processes.rAndD'),
          productRoadmap: t('humanPlus.productManagementSection.managementPhases.productDevelopment.processes.productRoadmap')
        }
      },
      marketStrategy: {
        title: t('humanPlus.productManagementSection.managementPhases.marketStrategy.title'),
        description: t('humanPlus.productManagementSection.managementPhases.marketStrategy.description'),
        processes: {
          productPositioning: t('humanPlus.productManagementSection.managementPhases.marketStrategy.processes.productPositioning'),
          competitorAnalysis: t('humanPlus.productManagementSection.managementPhases.marketStrategy.processes.competitorAnalysis'),
          licensingStrategy: t('humanPlus.productManagementSection.managementPhases.marketStrategy.processes.licensingStrategy'),
          roadmapPlanning: t('humanPlus.productManagementSection.managementPhases.marketStrategy.processes.roadmapPlanning'),
          yearlyPlanning: t('humanPlus.productManagementSection.managementPhases.marketStrategy.processes.yearlyPlanning')
        }
      },
      launchPreparation: {
        title: t('humanPlus.productManagementSection.managementPhases.launchPreparation.title'),
        description: t('humanPlus.productManagementSection.managementPhases.launchPreparation.description'),
        processes: {
          marketingStrategy: t('humanPlus.productManagementSection.managementPhases.launchPreparation.processes.marketingStrategy'),
          launchPreparation: t('humanPlus.productManagementSection.managementPhases.launchPreparation.processes.launchPreparation'),
          communicationPlan: t('humanPlus.productManagementSection.managementPhases.launchPreparation.processes.communicationPlan'),
          organizationalChart: t('humanPlus.productManagementSection.managementPhases.launchPreparation.processes.organizationalChart'),
          changeManagement: t('humanPlus.productManagementSection.managementPhases.launchPreparation.processes.changeManagement')
        }
      },
      postMarket: {
        title: t('humanPlus.productManagementSection.managementPhases.postMarket.title'),
        description: t('humanPlus.productManagementSection.managementPhases.postMarket.description'),
        processes: {
          postMarketSurveillance: t('humanPlus.productManagementSection.managementPhases.postMarket.processes.postMarketSurveillance'),
          roadmapReleases: t('humanPlus.productManagementSection.managementPhases.postMarket.processes.roadmapReleases'),
          changeManagement: t('humanPlus.productManagementSection.managementPhases.postMarket.processes.changeManagement'),
          communicationPlan: t('humanPlus.productManagementSection.managementPhases.postMarket.processes.communicationPlan')
        }
      }
    },
    components: {
      roadmapPlanning: {
        title: t('humanPlus.productManagementSection.components.roadmapPlanning.title'),
        description: t('humanPlus.productManagementSection.components.roadmapPlanning.description'),
        elements: {
          feasibility: {
            title: t('humanPlus.productManagementSection.components.roadmapPlanning.elements.feasibility.title'),
            description: t('humanPlus.productManagementSection.components.roadmapPlanning.elements.feasibility.description')
          },
          positioning: {
            title: t('humanPlus.productManagementSection.components.roadmapPlanning.elements.positioning.title'),
            description: t('humanPlus.productManagementSection.components.roadmapPlanning.elements.positioning.description')
          },
          releases: {
            title: t('humanPlus.productManagementSection.components.roadmapPlanning.elements.releases.title'),
            description: t('humanPlus.productManagementSection.components.roadmapPlanning.elements.releases.description')
          },
          planning: {
            title: t('humanPlus.productManagementSection.components.roadmapPlanning.elements.planning.title'),
            description: t('humanPlus.productManagementSection.components.roadmapPlanning.elements.planning.description')
          }
        }
      }
    }
  };

  // Prepare translations for ProductManagementConsultation
  const productManagementConsultationTranslations = {
    title: t('humanPlus.productManagement.title'),
    subtitle: t('humanPlus.productManagement.subtitle'),
    introduction: t('humanPlus.productManagement.introduction'),
    cta: t('humanPlus.productManagement.cta'),
    cards: {
      strategy: {
        title: t('humanPlus.productManagement.cards.strategy.title'),
        description: t('humanPlus.productManagement.cards.strategy.description'),
        features: [
          t('humanPlus.productManagement.cards.strategy.features.0'),
          t('humanPlus.productManagement.cards.strategy.features.1'),
          t('humanPlus.productManagement.cards.strategy.features.2'),
          t('humanPlus.productManagement.cards.strategy.features.3'),
        ],
        cta: t('humanPlus.productManagement.cards.strategy.cta'),
      },
      agile: {
        title: t('humanPlus.productManagement.cards.agile.title'),
        description: t('humanPlus.productManagement.cards.agile.description'),
        features: [
          t('humanPlus.productManagement.cards.agile.features.0'),
          t('humanPlus.productManagement.cards.agile.features.1'),
          t('humanPlus.productManagement.cards.agile.features.2'),
          t('humanPlus.productManagement.cards.agile.features.3'),
        ],
        cta: t('humanPlus.productManagement.cards.agile.cta'),
      },
      leadership: {
        title: t('humanPlus.productManagement.cards.leadership.title'),
        description: t('humanPlus.productManagement.cards.leadership.description'),
        features: [
          t('humanPlus.productManagement.cards.leadership.features.0'),
          t('humanPlus.productManagement.cards.leadership.features.1'),
          t('humanPlus.productManagement.cards.leadership.features.2'),
          t('humanPlus.productManagement.cards.leadership.features.3'),
        ],
        cta: t('humanPlus.productManagement.cards.leadership.cta'),
      },
      design: {
        title: t('humanPlus.productManagement.cards.design.title'),
        description: t('humanPlus.productManagement.cards.design.description'),
        features: [
          t('humanPlus.productManagement.cards.design.features.0'),
          t('humanPlus.productManagement.cards.design.features.1'),
          t('humanPlus.productManagement.cards.design.features.2'),
          t('humanPlus.productManagement.cards.design.features.3'),
        ],
        cta: t('humanPlus.productManagement.cards.design.cta'),
      },
      analytics: {
        title: t('humanPlus.productManagement.cards.analytics.title'),
        description: t('humanPlus.productManagement.cards.analytics.description'),
        features: [
          t('humanPlus.productManagement.cards.analytics.features.0'),
          t('humanPlus.productManagement.cards.analytics.features.1'),
          t('humanPlus.productManagement.cards.analytics.features.2'),
          t('humanPlus.productManagement.cards.analytics.features.3'),
        ],
        cta: t('humanPlus.productManagement.cards.analytics.cta'),
      },
    },
    caseStudy: {
      title: t('humanPlus.productManagement.caseStudy.title'),
      brief: t('humanPlus.productManagement.caseStudy.brief'),
      metrics: [
        t('humanPlus.productManagement.caseStudy.metrics.0'),
        t('humanPlus.productManagement.caseStudy.metrics.1'),
        t('humanPlus.productManagement.caseStudy.metrics.2'),
      ],
      cta: t('humanPlus.productManagement.caseStudy.cta'),
    },
    contact: {
      title: t('humanPlus.productManagement.contact.title'),
      description: t('humanPlus.productManagement.contact.description'),
      primaryCta: t('humanPlus.productManagement.contact.primaryCta'),
      secondaryCta: t('humanPlus.productManagement.contact.secondaryCta'),
    },
  };

  // Combine translations into a single object
  const translations = {
    productManagement: productManagementConsultationTranslations,
    productManagementSection: productManagementSectionTranslations
  };

  return <HumanPlusPageContent translations={translations} />;
}
