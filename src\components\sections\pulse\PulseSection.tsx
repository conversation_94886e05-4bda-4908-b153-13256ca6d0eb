'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import useIntersectionObserver from '@/hooks/useIntersectionObserver';
import ParticleBackground from '@/components/animations/ParticleBackground';
import { Info } from 'lucide-react';

interface Technology {
  name: string;
  description: string;
  maturity: 'emerging' | 'growing' | 'maturing' | 'established';
  category: string;
  adoptionRate: string;
  industryImpact: Record<string, string>;
  businessApplications?: string[];
  predictedGrowth?: string;
  keyPlayers?: string[];
}

interface PulseSectionProps {
  translations?: {
    title?: string;
    subtitle?: string;
    howToUseTitle?: string;
    howToUseStep1Title?: string;
    howToUseStep1Desc?: string;
    howToUseStep2Title?: string;
    howToUseStep2Desc?: string;
    howToUseStep3Title?: string;
    howToUseStep3Desc?: string;
    timeRangeSelector?: {
      title?: string;
      options?: {
        sixMonths?: string;
        sixMonthsDesc?: string;
        oneYear?: string;
        oneYearDesc?: string;
        fiveYears?: string;
        fiveYearsDesc?: string;
      };
    };
    filters?: {
      title?: string;
      description?: string;
      options?: {
        ai?: string;
        aiDesc?: string;
        cloud?: string;
        cloudDesc?: string;
        iot?: string;
        iotDesc?: string;
        security?: string;
        securityDesc?: string;
        blockchain?: string;
        blockchainDesc?: string;
      };
    };
    maturityLevels?: {
      established?: string;
      maturing?: string;
      growing?: string;
      emerging?: string;
      descriptions?: {
        established?: string;
        maturing?: string;
        growing?: string;
        emerging?: string;
      };
    };
    technologies?: Record<string, Technology>;
    timelineTitle?: string;
    timelineDescription?: string;
    technologiesLabel?: string;
    noTechnologiesMessage?: string;
    resetFiltersLabel?: string;
    relevanceMeter?: {
      title?: string;
      description?: string;
      companySize?: {
        title?: string;
        description?: string;
        options?: {
          startup?: string;
          smb?: string;
          enterprise?: string;
        };
      };
      industry?: {
        title?: string;
        description?: string;
        options?: Record<string, string>;
      };
      cta?: string;
      ctaDescription?: string;
    };
  };
}

const PulseSection: React.FC<PulseSectionProps> = ({ translations }) => {
  const [timeRange, setTimeRange] = useState<string>('oneYear');
  const [activeFilters, setActiveFilters] = useState<string[]>(['ai', 'cloud', 'security']);
  const [selectedTechnology, setSelectedTechnology] = useState<string | null>(null);
  const [selectedCompanySize, setSelectedCompanySize] = useState<string | null>(null);
  const [selectedIndustry, setSelectedIndustry] = useState<string>('');
  const [relevanceScore, setRelevanceScore] = useState<number>(50);
  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 0.1 });

  // Define maturity level colors with consistency
  const maturityColors = {
    emerging: '#FF6D00', // orange
    growing: '#FFAB00', // amber
    maturing: '#00BFA5', // teal
    established: '#00B0FF', // light blue
  };

  // Define maturity level descriptions
  const maturityDescriptions: Record<string, string> = {
    established: translations?.maturityLevels?.descriptions?.established || '',
    maturing: translations?.maturityLevels?.descriptions?.maturing || '',
    growing: translations?.maturityLevels?.descriptions?.growing || '',
    emerging: translations?.maturityLevels?.descriptions?.emerging || ''
  };

  // Toggle filter selection
  const toggleFilter = (filter: string) => {
    if (activeFilters.includes(filter)) {
      setActiveFilters(activeFilters.filter(f => f !== filter));
    } else {
      setActiveFilters([...activeFilters, filter]);
    }
  };

  // Handle technology node click
  const handleTechnologyClick = (techKey: string) => {
    setSelectedTechnology(selectedTechnology === techKey ? null : techKey);
  };

  // Handle company size selection
  const handleCompanySizeSelect = (size: string) => {
    setSelectedCompanySize(selectedCompanySize === size ? null : size);
    updateRelevanceScore();
  };

  // Handle industry selection
  const handleIndustrySelect = (industry: string) => {
    setSelectedIndustry(industry);
    updateRelevanceScore();
  };

  // Update relevance score based on selections
  const updateRelevanceScore = () => {
    // Simple algorithm to calculate relevance (would be more sophisticated in real app)
    let score = 50; // baseline

    if (selectedCompanySize) {
      score += 15;
    }

    if (selectedIndustry) {
      score += 20;
    }

    // Add some randomness for demo purposes
    score += Math.floor(Math.random() * 15);

    // Ensure score is within 0-100 range
    score = Math.min(Math.max(score, 0), 100);

    setRelevanceScore(score);
  };

  // Effect to update relevance when selections change
  useEffect(() => {
    updateRelevanceScore();
  }, [selectedCompanySize, selectedIndustry]);

  // Get technologies based on active filters and time range
  const getFilteredTechnologies = () => {
    if (!translations?.technologies) return [];

    return Object.entries(translations.technologies)
      .filter(([_, tech]) => activeFilters.includes(tech.category))
      .map(([key, tech]) => ({
        key,
        ...tech
      }));
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <div className="bg-[#121826] text-white">


      {/* Main content section */}
      <section ref={ref} className="relative min-h-screen py-20" id="pulse-section">
        <div className="container mx-auto px-4 max-w-7xl">
        {/* Background particles */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute inset-0 opacity-10">
            <ParticleBackground
              color="#4FC3F7"
              secondaryColor="#7C4DFF"
              density="medium"
              speed="slow"
            />
          </div>
        </div>

        {/* Header with clearer purpose statement and introduction */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={isIntersecting ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
        >
          <div className="text-center mb-8">
            <h1 className="text-4xl md:text-5xl font-bold mb-4 text-white">
              {translations?.title}
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              {translations?.subtitle}
            </p>
          </div>

          {/* Introduction card with clearer explanation */}
          <div className="bg-gray-900/30 backdrop-blur-sm rounded-lg p-6 border border-gray-800 max-w-4xl mx-auto">
            <h2 className="text-xl font-semibold text-white mb-3">{translations?.howToUseTitle}</h2>
            <div className="grid md:grid-cols-3 gap-6 text-gray-300">
              <div className="flex flex-col items-center text-center p-3">
                <div className="w-12 h-12 rounded-full bg-blue-900/50 flex items-center justify-center mb-3 text-cyan-400 border border-cyan-400/30">1</div>
                <h3 className="font-medium text-white mb-2">{translations?.howToUseStep1Title}</h3>
                <p className="text-sm">{translations?.howToUseStep1Desc}</p>
              </div>
              <div className="flex flex-col items-center text-center p-3">
                <div className="w-12 h-12 rounded-full bg-blue-900/50 flex items-center justify-center mb-3 text-cyan-400 border border-cyan-400/30">2</div>
                <h3 className="font-medium text-white mb-2">{translations?.howToUseStep2Title}</h3>
                <p className="text-sm">{translations?.howToUseStep2Desc}</p>
              </div>
              <div className="flex flex-col items-center text-center p-3">
                <div className="w-12 h-12 rounded-full bg-blue-900/50 flex items-center justify-center mb-3 text-cyan-400 border border-cyan-400/30">3</div>
                <h3 className="font-medium text-white mb-2">{translations?.howToUseStep3Title}</h3>
                <p className="text-sm">{translations?.howToUseStep3Desc}</p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Main layout with clear sections */}
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Left sidebar - Controls */}
          <motion.div
            className="lg:w-1/4"
            initial={{ opacity: 0, x: -20 }}
            animate={isIntersecting ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <div className="sticky top-24 space-y-6">
              {/* Filter controls header */}
              <div className="bg-gradient-to-r from-blue-900/70 to-indigo-900/70 rounded-lg p-4 border border-blue-700/30">
                <h3 className="text-lg font-semibold text-white mb-1">{translations?.filters?.title}</h3>
                <p className="text-xs text-gray-300">{translations?.filters?.description}</p>
              </div>

              {/* Time Range Selector */}
              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800">
                <div className="p-4 border-b border-gray-800 flex justify-between items-center">
                  <h3 className="text-white font-medium flex items-center gap-2">
                    {translations?.timeRangeSelector?.title}
                    <span className="group relative">
                      <Info size={16} className="text-gray-400 cursor-help" />
                    </span>
                  </h3>
                  <span className="text-xs text-cyan-400 bg-cyan-900/30 px-2 py-1 rounded-full">
                    Impact Horizon
                  </span>
                </div>
                <div className="p-4">
                  <div className="flex flex-col gap-2">
                    {[
                      { key: 'sixMonths', label: translations?.timeRangeSelector?.options?.sixMonths, description: translations?.timeRangeSelector?.options?.sixMonthsDesc },
                      { key: 'oneYear', label: translations?.timeRangeSelector?.options?.oneYear, description: translations?.timeRangeSelector?.options?.oneYearDesc },
                      { key: 'fiveYears', label: translations?.timeRangeSelector?.options?.fiveYears, description: translations?.timeRangeSelector?.options?.fiveYearsDesc }
                    ].map(option => (
                      <button
                        key={option.key}
                        className={`px-4 py-2 rounded-md text-sm transition-colors w-full text-left ${
                          timeRange === option.key
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                        }`}
                        onClick={() => setTimeRange(option.key)}
                      >
                        <div className="font-medium">{option.label}</div>
                        <div className="text-xs opacity-80">{option.description}</div>
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Category Filters */}
              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800">
                <div className="p-4 border-b border-gray-800 flex justify-between items-center">
                  <h3 className="text-white font-medium flex items-center gap-2">
                    {translations?.filters?.title}
                    <span className="group relative">
                      <Info size={16} className="text-gray-400 cursor-help" />
                      <span className="absolute left-full ml-2 top-0 w-48 bg-gray-800 p-2 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10">
                        {translations?.filters?.description}
                      </span>
                    </span>
                  </h3>
                  <span className="text-xs text-cyan-400 bg-cyan-900/30 px-2 py-1 rounded-full">
                    {activeFilters.length} Selected
                  </span>
                </div>
                <div className="p-4">
                  <div className="flex flex-col gap-2">
                    {[
                      { key: 'ai', label: translations?.filters?.options?.ai, color: '#4FC3F7', icon: '🧠', description: translations?.filters?.options?.aiDesc },
                      { key: 'cloud', label: translations?.filters?.options?.cloud, color: '#7C4DFF', icon: '☁️', description: translations?.filters?.options?.cloudDesc },
                      { key: 'iot', label: translations?.filters?.options?.iot, color: '#00BFA5', icon: '🔌', description: translations?.filters?.options?.iotDesc },
                      { key: 'security', label: translations?.filters?.options?.security, color: '#FF6D00', icon: '🔒', description: translations?.filters?.options?.securityDesc },
                      { key: 'blockchain', label: translations?.filters?.options?.blockchain, color: '#FFAB00', icon: '⛓️', description: translations?.filters?.options?.blockchainDesc }
                    ].map(filter => (
                      <button
                        key={filter.key}
                        className={`px-4 py-2 rounded-md text-sm transition-colors flex items-center gap-3 ${
                          activeFilters.includes(filter.key)
                            ? 'bg-opacity-100 text-white'
                            : 'bg-opacity-30 text-gray-300 hover:bg-opacity-50'
                        }`}
                        style={{
                          backgroundColor: activeFilters.includes(filter.key)
                            ? filter.color
                            : 'rgba(75, 85, 99, 0.3)'
                        }}
                        onClick={() => toggleFilter(filter.key)}
                      >
                        <span className="text-xl">{filter.icon}</span>
                        <div className="text-left">
                          <div className="font-medium">{filter.label}</div>
                          <div className="text-xs opacity-80">{filter.description}</div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Maturity Level Legend */}
              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800">
                <div className="p-4 border-b border-gray-800">
                  <h3 className="text-white font-medium">Maturity Levels</h3>
                </div>
                <div className="p-4">
                  <div className="space-y-4">
                    {[
                      { key: 'established', label: translations?.maturityLevels?.established, color: maturityColors.established },
                      { key: 'maturing', label: translations?.maturityLevels?.maturing, color: maturityColors.maturing },
                      { key: 'growing', label: translations?.maturityLevels?.growing, color: maturityColors.growing },
                      { key: 'emerging', label: translations?.maturityLevels?.emerging, color: maturityColors.emerging }
                    ].map(level => (
                      <div key={level.key} className="group relative">
                        <div className="flex items-center gap-3 p-2 rounded hover:bg-gray-800/50">
                          <div className="w-4 h-4 rounded-full" style={{ backgroundColor: level.color }}></div>
                          <div>
                            <div className="text-gray-200 font-medium">{level.label}</div>
                            <div className="text-xs text-gray-400">{maturityDescriptions[level.key]}</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Center column - Timeline */}
          <motion.div
            className="lg:w-2/4"
            initial={{ opacity: 0 }}
            animate={isIntersecting ? { opacity: 1 } : {}}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            {/* Timeline header */}
            <div className="bg-gradient-to-r from-blue-900/70 to-indigo-900/70 rounded-lg p-4 mb-6 border border-blue-700/30">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold text-white">{translations?.timelineTitle}</h2>
                <div className="text-xs text-cyan-400 bg-cyan-900/30 px-2 py-1 rounded-full">
                  {getFilteredTechnologies().length} {translations?.technologiesLabel}
                </div>
              </div>
              <p className="text-xs text-gray-300 mt-1">
                {translations?.timelineDescription}
              </p>
            </div>

            <div className="relative">
              {/* Vertical Timeline */}
              <div className="absolute left-8 top-0 bottom-0 w-1 bg-gradient-to-b from-[#4FC3F7] to-[#7C4DFF]">
                {/* Pulsing dots along the timeline */}
                {[0, 1, 2, 3, 4].map((i) => (
                  <div
                    key={i}
                    className="absolute w-2 h-2 rounded-full bg-white left-1/2 transform -translate-x-1/2"
                    style={{
                      top: `${15 + i * 20}%`,
                      animation: `pulse 3s infinite ${i * 0.5}s`,
                      opacity: 0.7
                    }}
                  ></div>
                ))}
              </div>

              {/* Add CSS for the pulse animation */}
              <style jsx>{`
                @keyframes pulse {
                  0% { transform: translate(-50%, 0) scale(0.5); opacity: 0.2; }
                  50% { transform: translate(-50%, 0) scale(1.5); opacity: 0.8; }
                  100% { transform: translate(-50%, 0) scale(0.5); opacity: 0.2; }
                }
                @keyframes glow {
                  0% { box-shadow: 0 0 5px rgba(79, 195, 247, 0.5); }
                  50% { box-shadow: 0 0 20px rgba(79, 195, 247, 0.8); }
                  100% { box-shadow: 0 0 5px rgba(79, 195, 247, 0.5); }
                }
              `}</style>

              {/* Technology nodes with enhanced cards */}
              <motion.div
                className="pl-16 relative py-6"
                variants={containerVariants}
                initial="hidden"
                animate={isIntersecting ? "visible" : "hidden"}
              >
                {getFilteredTechnologies().length === 0 ? (
                  <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-8 text-center border border-gray-800">
                    <div className="text-gray-400 mb-3">{translations?.noTechnologiesMessage}</div>
                    <button
                      className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm"
                      onClick={() => setActiveFilters(['ai', 'cloud', 'security'])}
                    >
                      {translations?.resetFiltersLabel}
                    </button>
                  </div>
                ) : (
                  getFilteredTechnologies().map((tech) => (
                    <motion.div
                      key={tech.key}
                      className="mb-10 relative"
                      variants={itemVariants}
                    >
                      {/* Timeline node */}
                      <div
                        className="absolute left-0 w-6 h-6 rounded-full cursor-pointer border-2 transform -translate-x-[52px] z-10"
                        style={{
                          backgroundColor: selectedTechnology === tech.key ? maturityColors[tech.maturity] : '#1E293B',
                          borderColor: maturityColors[tech.maturity],
                          boxShadow: selectedTechnology === tech.key ? `0 0 15px ${maturityColors[tech.maturity]}` : 'none',
                          animation: selectedTechnology === tech.key ? 'glow 2s infinite' : 'none'
                        }}
                        onClick={() => handleTechnologyClick(tech.key)}
                      ></div>

                      {/* Technology card with expanded content */}
                      <div
                        className={`bg-gray-900/50 backdrop-blur-sm rounded-lg border transition-all ${
                          selectedTechnology === tech.key ? 'border-opacity-100 shadow-xl' : 'border-opacity-30'
                        }`}
                        style={{
                          borderColor: maturityColors[tech.maturity],
                          boxShadow: selectedTechnology === tech.key ? `0 0 20px rgba(0, 0, 0, 0.5)` : 'none'
                        }}
                      >
                        {/* Card header - always visible */}
                        <div
                          className="p-5 cursor-pointer"
                          onClick={() => handleTechnologyClick(tech.key)}
                        >
                          <div className="flex justify-between items-center mb-3">
                            <h3 className="text-xl font-semibold text-white">{tech.name}</h3>
                            <div className="flex items-center gap-2">
                              <span
                                className="text-xs px-2 py-1 rounded-full"
                                style={{ backgroundColor: maturityColors[tech.maturity] }}
                              >
                                {tech.maturity.charAt(0).toUpperCase() + tech.maturity.slice(1)}
                              </span>
                            </div>
                          </div>
                          <p className="text-gray-300 text-sm">{tech.description}</p>

                          {/* Quick stats - always visible */}
                          <div className="flex flex-wrap gap-4 mt-4 text-xs">
                            <div className="flex items-center gap-1">
                              <div className="w-2 h-2 rounded-full bg-green-400"></div>
                              <span className="text-gray-300">Growth: <span className="text-green-400">{tech.adoptionRate}</span></span>
                            </div>
                            <div className="flex items-center gap-1">
                              <div className="w-2 h-2 rounded-full" style={{ backgroundColor: maturityColors[tech.maturity] }}></div>
                              <span className="text-gray-300">Category: <span className="text-white">{tech.category}</span></span>
                            </div>
                          </div>
                        </div>

                        {/* Expanded details */}
                        {selectedTechnology === tech.key && (
                          <motion.div
                            className="px-5 pb-5 pt-2 border-t border-gray-700"
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            transition={{ duration: 0.3 }}
                          >
                            {/* Tabs for different sections */}
                            <div className="flex border-b border-gray-700 mb-4">
                              <button className="px-4 py-2 text-sm font-medium text-cyan-400 border-b-2 border-cyan-400">
                                Business Impact
                              </button>
                              <button className="px-4 py-2 text-sm font-medium text-gray-400 hover:text-white">
                                Technical Details
                              </button>
                            </div>

                            {/* Industry Impact */}
                            <div className="mb-4">
                              <div className="flex justify-between items-center mb-2">
                                <span className="text-sm text-white font-medium">Industry Impact</span>
                                <span className="text-xs text-gray-400">Relative impact level</span>
                              </div>
                              <div className="space-y-2">
                                {Object.entries(tech.industryImpact).map(([industry, impact]) => (
                                  <div key={industry} className="flex justify-between items-center">
                                    <span className="text-xs text-gray-300 capitalize">{industry}</span>
                                    <div className="w-32 bg-gray-800 rounded-full h-2">
                                      <div
                                        className="h-2 rounded-full"
                                        style={{
                                          backgroundColor: maturityColors[tech.maturity],
                                          width: impact === 'Very High' ? '100%' :
                                                impact === 'High' ? '75%' :
                                                impact === 'Medium' ? '50%' :
                                                impact === 'Low' ? '25%' : '0%'
                                        }}
                                      ></div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>

                            {/* Business Applications */}
                            {tech.businessApplications && tech.businessApplications.length > 0 && (
                              <div className="mb-4">
                                <div className="flex justify-between items-center mb-2">
                                  <span className="text-sm text-white font-medium">Business Applications</span>
                                  <span className="text-xs text-cyan-400 bg-cyan-900/30 px-2 py-1 rounded-full">
                                    {tech.businessApplications.length} Use Cases
                                  </span>
                                </div>
                                <div className="bg-gray-800/50 rounded-lg p-3">
                                  <ul className="list-disc list-inside space-y-1">
                                    {tech.businessApplications.map((app, i) => (
                                      <li key={i} className="text-xs text-gray-300">{app}</li>
                                    ))}
                                  </ul>
                                </div>
                              </div>
                            )}

                            {/* Bottom section with key players and growth */}
                            <div className="flex flex-col md:flex-row gap-4">
                              {/* Key Players */}
                              {tech.keyPlayers && tech.keyPlayers.length > 0 && (
                                <div className="flex-1">
                                  <span className="text-sm text-white font-medium block mb-2">Key Players</span>
                                  <div className="flex flex-wrap gap-2">
                                    {tech.keyPlayers.map((player, i) => (
                                      <span
                                        key={i}
                                        className="text-xs bg-gray-800 px-2 py-1 rounded-full text-gray-300"
                                      >
                                        {player}
                                      </span>
                                    ))}
                                  </div>
                                </div>
                              )}

                              {/* Predicted Growth */}
                              {tech.predictedGrowth && (
                                <div className="md:w-1/3">
                                  <span className="text-sm text-white font-medium block mb-2">Predicted Growth</span>
                                  <div className="bg-gray-800/50 rounded-lg p-3 flex items-center justify-center">
                                    <span className="text-lg font-bold text-green-400">
                                      {tech.predictedGrowth}
                                    </span>
                                  </div>
                                </div>
                              )}
                            </div>
                          </motion.div>
                        )}
                      </div>
                    </motion.div>
                  ))
                )}
              </motion.div>
            </div>
          </motion.div>
          {/* Relevance Meter */}
          <motion.div
            className="hidden lg:block w-64 sticky top-24 self-start"
            initial={{ opacity: 0, x: 20 }}
            animate={isIntersecting ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            {/* Relevance Meter Header */}
            <div className="bg-gradient-to-r from-blue-900/70 to-indigo-900/70 rounded-lg p-4 mb-6 border border-blue-700/30">
              <h3 className="text-lg font-semibold text-white mb-1">
                {translations?.relevanceMeter?.title}
              </h3>
              <p className="text-xs text-gray-300">
                {translations?.relevanceMeter?.description}
              </p>
            </div>

            <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800">
              <div className="p-4 border-b border-gray-800">
                <p className="text-sm text-gray-300">
                  {translations?.relevanceMeter?.description}
                </p>
              </div>

              {/* Company Size */}
              <div className="p-4 border-b border-gray-800">
                <h4 className="text-white font-medium mb-3 flex items-center gap-2">
                  {translations?.relevanceMeter?.companySize?.title}
                  <span className="group relative">
                    <Info size={14} className="text-gray-400 cursor-help" />
                    <span className="absolute right-full mr-2 top-0 w-48 bg-gray-800 p-2 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10">
                      {translations?.relevanceMeter?.companySize?.description}
                    </span>
                  </span>
                </h4>
                <div className="grid grid-cols-3 gap-2">
                  {[
                    { key: 'startup', label: translations?.relevanceMeter?.companySize?.options?.startup, icon: '🚀' },
                    { key: 'smb', label: translations?.relevanceMeter?.companySize?.options?.smb, icon: '🏢' },
                    { key: 'enterprise', label: translations?.relevanceMeter?.companySize?.options?.enterprise, icon: '🏙️' }
                  ].map(option => (
                    <button
                      key={option.key}
                      className={`px-2 py-2 rounded-md text-sm transition-colors flex flex-col items-center ${
                        selectedCompanySize === option.key
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                      }`}
                      onClick={() => handleCompanySizeSelect(option.key)}
                    >
                      <span className="text-lg mb-1">{option.icon}</span>
                      <span className="text-xs font-medium">{option.label}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Industry */}
              <div className="p-4 border-b border-gray-800">
                <h4 className="text-white font-medium mb-3 flex items-center gap-2">
                  {translations?.relevanceMeter?.industry?.title}
                  <span className="group relative">
                    <Info size={14} className="text-gray-400 cursor-help" />
                    <span className="absolute right-full mr-2 top-0 w-48 bg-gray-800 p-2 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10">
                      {translations?.relevanceMeter?.industry?.description}
                    </span>
                  </span>
                </h4>
                <select
                  className="w-full bg-gray-800 border border-gray-700 rounded-md px-3 py-2 text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={selectedIndustry}
                  onChange={(e) => handleIndustrySelect(e.target.value)}
                >
                  <option value="">Select industry</option>
                  {translations?.relevanceMeter?.industry?.options &&
                    Object.entries(translations.relevanceMeter.industry.options).map(([key, label]) => (
                      <option key={key} value={key}>{label}</option>
                    ))
                  }
                </select>
              </div>

              {/* Relevance Visualization */}
              <div className="p-4 border-b border-gray-800">
                <div className="flex justify-between mb-3">
                  <h4 className="text-white font-medium">Relevance Score</h4>
                  <div className="flex items-center gap-1">
                    <div className={`w-2 h-2 rounded-full ${
                      relevanceScore > 75 ? 'bg-green-400' :
                      relevanceScore > 50 ? 'bg-cyan-400' :
                      relevanceScore > 25 ? 'bg-yellow-400' : 'bg-red-400'
                    }`}></div>
                    <span className={`text-sm font-medium ${
                      relevanceScore > 75 ? 'text-green-400' :
                      relevanceScore > 50 ? 'text-cyan-400' :
                      relevanceScore > 25 ? 'text-yellow-400' : 'text-red-400'
                    }`}>{relevanceScore}%</span>
                  </div>
                </div>

                <div className="w-full h-4 bg-gray-800 rounded-full overflow-hidden mb-2">
                  <div
                    className={`h-full ${
                      relevanceScore > 75 ? 'bg-gradient-to-r from-green-500 to-green-400' :
                      relevanceScore > 50 ? 'bg-gradient-to-r from-[#4FC3F7] to-[#7C4DFF]' :
                      relevanceScore > 25 ? 'bg-gradient-to-r from-yellow-500 to-yellow-400' :
                      'bg-gradient-to-r from-red-500 to-red-400'
                    }`}
                    style={{ width: `${relevanceScore}%` }}
                  ></div>
                </div>

                <div className="flex justify-between text-xs text-gray-400 mb-2">
                  <span>Low Relevance</span>
                  <span>High Relevance</span>
                </div>

                <div className="text-xs text-gray-400">
                  {relevanceScore > 75 ? 'These technologies are highly relevant to your business context' :
                   relevanceScore > 50 ? 'These technologies have good relevance to your business context' :
                   relevanceScore > 25 ? 'These technologies have moderate relevance to your business' :
                   'These technologies may have limited relevance to your current context'}
                </div>
              </div>

              {/* CTA Section */}
              <div className="p-4">
                <div className="bg-gradient-to-r from-blue-900/30 to-indigo-900/30 rounded-lg p-3 mb-4 border border-blue-700/20">
                  <h4 className="text-white font-medium mb-1 flex items-center gap-2">
                    <span className="text-cyan-400">✓</span> Personalized Report
                  </h4>
                  <p className="text-xs text-gray-300 mb-2">
                    {translations?.relevanceMeter?.ctaDescription}
                  </p>
                  <button className="w-full bg-gradient-to-r from-[#4FC3F7] to-[#7C4DFF] text-white font-medium py-2 px-4 rounded-md hover:opacity-90 transition-opacity text-sm">
                    {translations?.relevanceMeter?.cta}
                  </button>
                </div>

                <div className="text-xs text-gray-500 text-center">
                  Based on your selections and our proprietary technology impact analysis
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  </div>
  );
};

export default PulseSection;
