import { getServerTranslations } from '@/lib/i18n';
import MLArchitecturePageContent from '@/components/sections/MLArchitecturePageContent';

export default async function ArchitecturesPage({ params }: { params: { locale: string } | Promise<{ locale: string }> }) {
  // Handle both Promise and direct object cases
  const resolvedParams = params instanceof Promise ? await params : params

  // Get translations on the server
  const { t } = await getServerTranslations(resolvedParams.locale, ['language']);

  // Debug the translation function
  console.log('Sample component detail translation:', t('services.mlArchitecture.pageContent.componentDetails.modelBuilder', { returnObjects: true }));

  // Prepare translations to pass as props
  const translations = {
    title: t('services.mlArchitecture.title'),
    subtitle: t('services.mlArchitecture.pageContent.subtitle'),
    exploreButton: t('services.mlArchitecture.pageContent.launchExplorer'),
    phases: {
      dataIngestion: t('services.mlArchitecture.phases.dataIngestion'),
      modelTraining: t('services.mlArchitecture.phases.modelTraining'),
      deployment: t('services.mlArchitecture.phases.deployment'),
      monitoring: t('services.mlArchitecture.phases.monitoring'),
    },
    viewModes: {
      conceptual: t('services.mlArchitecture.viewModes.conceptual'),
      implementation: t('services.mlArchitecture.viewModes.implementation'),
    },
    buttons: {
      zoomIn: t('services.mlArchitecture.buttons.zoomIn'),
      zoomOut: t('services.mlArchitecture.buttons.zoomOut'),
      fullscreen: t('services.mlArchitecture.buttons.fullscreen'),
      close: t('services.mlArchitecture.buttons.close'),
      runData: t('services.mlArchitecture.buttons.runData'),
      reset: t('services.mlArchitecture.buttons.reset'),
    }
  };

  return <MLArchitecturePageContent translations={translations} />;
}
