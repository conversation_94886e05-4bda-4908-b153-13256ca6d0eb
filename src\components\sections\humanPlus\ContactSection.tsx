'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Button from '@/components/ui/Button';

interface ContactSectionProps {
  title: string;
  description: string;
  primaryCta: string;
  secondaryCta: string;
  isIntersecting: boolean;
}

export default function ContactSection({
  title,
  description,
  primaryCta,
  secondaryCta,
  isIntersecting,
}: ContactSectionProps) {
  return (
    <motion.div
      className="bg-gray-900/40 backdrop-blur-sm rounded-xl border border-purple-700/30 p-8 md:p-12 overflow-hidden"
      initial={{ opacity: 0, y: 20 }}
      animate={isIntersecting ? { opacity: 1, y: 0 } : {}}
      transition={{ duration: 0.8, delay: 0.3 }}
    >
      <div className="flex flex-col md:flex-row items-center justify-between gap-8">
        <div className="md:w-2/3">
          <h2 className="text-2xl md:text-3xl font-bold mb-4 text-white">
            {title}
          </h2>
          <p className="text-gray-300 text-lg mb-6">
            {description}
          </p>

          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              variant="secondary"
              size="lg"
              hasGlow
            >
              {primaryCta}
            </Button>

            <button
              className="inline-flex items-center text-white hover:text-gray-300 transition-colors duration-300 mt-2 sm:mt-0 sm:ml-4"
            >
              <svg
                className="mr-2 w-5 h-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
              </svg>
              {secondaryCta}
            </button>
          </div>
        </div>

        <div className="md:w-1/3 flex justify-center">
          <div className="relative">
            <div className="w-24 h-24 rounded-full bg-gradient-to-br from-cyan-400 to-purple-500 flex items-center justify-center">
              <span className="text-4xl">👤</span>
            </div>
            <div className="mt-4 text-center">
              <p className="text-white font-semibold">Product Practice Lead</p>
              <p className="text-gray-400 text-sm">Senior Consultant</p>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
