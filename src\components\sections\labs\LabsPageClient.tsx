'use client';

import { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import CryptoBotSection from '@/components/sections/labs/CryptoBotSection';

type LabsPageClientProps = {
  translations: {
    title: string;
    subtitle: string;
    cryptoBot: any; // Using any for simplicity, but you could define a more specific type
  };
};

export default function LabsPageClient({ translations }: LabsPageClientProps) {
  const contentSectionRef = useRef<HTMLDivElement>(null);

  // Log when the page loads
  useEffect(() => {
    console.log('Labs page loaded');
  }, []);

  // Sticky header component
  const LabsHeader = () => (
    <div
      id="labs-header"
      className="sticky w-full left-0 right-0 bg-black/90 backdrop-blur-lg py-6 border-b border-gray-800"
      style={{
        position: 'sticky',
        top: 'var(--nav-sticky-top)',
        zIndex: 'var(--service-nav-z-index)'
      }}
    >
      <div className="container mx-auto px-4">
        <motion.h2
          className="text-3xl md:text-4xl font-bold mb-6 text-center bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-500"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6 }}
        >
          {translations.title}
        </motion.h2>

        <motion.p
          className="text-lg text-gray-300 max-w-3xl mx-auto text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {translations.subtitle}
        </motion.p>
      </div>
    </div>
  );

  return (
    <>
      {/* Header section with title and description */}
      <section className="relative bg-black pt-32 pb-8 labs-page">
        <div className="container mx-auto px-4">
          <motion.h2
            className="text-3xl md:text-4xl font-bold mb-6 text-center bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-500"
            initial={{ opacity: 0, y: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            {translations.title}
          </motion.h2>

          <motion.p
            className="text-lg text-gray-300 max-w-3xl mx-auto text-center mb-4"
            initial={{ opacity: 0, y: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {translations.subtitle}
          </motion.p>
        </div>
      </section>

      {/* Integrated sticky header section */}
      <LabsHeader />

      {/* Content section */}
      <section className="relative bg-black pb-20">
        <div className="container mx-auto px-4" style={{ zIndex: 'var(--content-z-index)' }}>
          <div id="labs-content" className="w-full pt-8" ref={contentSectionRef}>
            <CryptoBotSection translations={translations.cryptoBot} />
          </div>
        </div>
      </section>
    </>
  );
}
