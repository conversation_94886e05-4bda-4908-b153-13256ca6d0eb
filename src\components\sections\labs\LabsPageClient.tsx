'use client';

import { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import CryptoBotSection from '@/components/sections/labs/CryptoBotSection';

type LabsPageClientProps = {
  translations: {
    title: string;
    subtitle: string;
    cryptoBot: any; // Using any for simplicity, but you could define a more specific type
  };
};

export default function LabsPageClient({ translations }: LabsPageClientProps) {
  const contentSectionRef = useRef<HTMLDivElement>(null);
  const headerSectionRef = useRef<HTMLDivElement>(null);
  const [showStickyHeader, setShowStickyHeader] = useState(false);

  // Use scroll event listener to detect when the initial header goes out of view
  useEffect(() => {
    console.log('Labs page loaded - setting up scroll listener');

    const handleScroll = () => {
      if (headerSectionRef.current) {
        const headerRect = headerSectionRef.current.getBoundingClientRect();
        const headerBottom = headerRect.bottom;

        // Show sticky header when the original header is completely out of view
        const shouldShowSticky = headerBottom < 0;

        console.log('Scroll event:', {
          headerBottom,
          shouldShowSticky,
          currentShowSticky: showStickyHeader
        });

        if (shouldShowSticky !== showStickyHeader) {
          console.log('Changing sticky header visibility to:', shouldShowSticky);
          setShowStickyHeader(shouldShowSticky);
        }
      }
    };

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll);

    // Initial check
    handleScroll();

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [showStickyHeader]);

  // Debug sticky header state changes
  useEffect(() => {
    console.log('Sticky header state changed:', showStickyHeader);
  }, [showStickyHeader]);

  // Sticky header component - only show when initial header is out of view
  const LabsHeader = () => {
    console.log('LabsHeader rendering, showStickyHeader:', showStickyHeader);

    if (!showStickyHeader) {
      console.log('LabsHeader: Not rendering because showStickyHeader is false');
      return null;
    }

    console.log('LabsHeader: Rendering sticky header');
    return (
      <div
        id="labs-header"
        className="sticky w-full left-0 right-0 bg-black/90 backdrop-blur-lg py-6 transition-all duration-300"
        style={{
          position: 'sticky',
          top: 'var(--nav-sticky-top)',
          zIndex: 'var(--service-nav-z-index)'
        }}
      >
        <div className="container mx-auto px-4">
          <motion.h2
            className="text-3xl md:text-4xl font-bold mb-6 text-center bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-500"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {translations.title}
          </motion.h2>

          <motion.p
            className="text-lg text-gray-300 max-w-3xl mx-auto text-center"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            {translations.subtitle}
          </motion.p>
        </div>
      </div>
    );
  };

  return (
    <>
      {/* Header section with title and description - observed for intersection */}
      <section ref={headerObserverRef} className="relative bg-black pt-32 pb-16 labs-page">
        <div className="container mx-auto px-4">
          <motion.h2
            className="text-3xl md:text-4xl font-bold mb-6 text-center bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-500"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {translations.title}
          </motion.h2>

          <motion.p
            className="text-lg text-gray-300 max-w-3xl mx-auto text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {translations.subtitle}
          </motion.p>
        </div>
      </section>

      {/* Sticky header section - only shows when initial header is out of view */}
      <LabsHeader />

      {/* Content section */}
      <section className="relative bg-black pb-20">
        <div className="container mx-auto px-4" style={{ zIndex: 'var(--content-z-index)' }}>
          <div id="labs-content" className="w-full pt-8" ref={contentSectionRef}>
            <CryptoBotSection translations={translations.cryptoBot} />
          </div>
        </div>
      </section>
    </>
  );
}
