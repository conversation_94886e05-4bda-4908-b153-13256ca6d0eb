'use client';

import { useEffect } from 'react';
import { motion } from 'framer-motion';
import CryptoBotSection from '@/components/sections/labs/CryptoBotSection';

type LabsPageClientProps = {
  translations: {
    title: string;
    subtitle: string;
    cryptoBot: any; // Using any for simplicity, but you could define a more specific type
  };
};

export default function LabsPageClient({ translations }: LabsPageClientProps) {
  // Log when the page loads
  useEffect(() => {
    console.log('Labs page loaded');
  }, []);

  return (
    <section className="relative min-h-screen bg-black pt-32 pb-20">
      <div className="container mx-auto px-4">
        <motion.h2
          className="text-3xl md:text-4xl font-bold mb-6 text-center bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-500"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6 }}
        >
          {translations.title}
        </motion.h2>

        <motion.p
          className="text-lg text-gray-300 max-w-3xl mx-auto text-center mb-16"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {translations.subtitle}
        </motion.p>

        {/* CryptoBot section */}
        <CryptoBotSection translations={translations.cryptoBot} />
      </div>
    </section>
  );
}
