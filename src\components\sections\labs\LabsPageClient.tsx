'use client';

import { useRef } from 'react';
import { motion } from 'framer-motion';
import CryptoBotSection from '@/components/sections/labs/CryptoBotSection';

type LabsPageClientProps = {
  translations: {
    title: string;
    subtitle: string;
    cryptoBot: any; // Using any for simplicity, but you could define a more specific type
  };
};

export default function LabsPageClient({ translations }: LabsPageClientProps) {
  const contentSectionRef = useRef<HTMLDivElement>(null);

  return (
    <>
      {/* Single sticky header section */}
      <section
        className="sticky bg-black pt-0 pb-8 labs-page"
        style={{
          position: 'sticky',
          top: 'var(--nav-sticky-top)',
          zIndex: 'var(--service-nav-z-index)'
        }}
      >
        <div className="container mx-auto px-4">
          <motion.h2
            className="text-3xl md:text-4xl font-bold mb-6 text-center bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-500"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {translations.title}
          </motion.h2>

          <motion.p
            className="text-lg text-gray-300 max-w-3xl mx-auto text-center mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {translations.subtitle}
          </motion.p>
        </div>
      </section>

      {/* Content section */}
      <section className="relative bg-black pb-64">
        <div className="container mx-auto px-4" style={{ zIndex: 'var(--content-z-index)' }}>
          <div id="labs-content" className="w-full" ref={contentSectionRef}>
            <CryptoBotSection translations={translations.cryptoBot} />
          </div>
        </div>
      </section>
    </>
  );
}
