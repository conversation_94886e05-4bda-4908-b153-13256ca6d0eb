'use client';

import React from 'react';
import { motion, Variants } from 'framer-motion';

interface ProductManagementCardProps {
  title: string;
  description: string;
  features: string[];
  cta: string;
  icon: string;
  color: string;
  bgGradient: string;
  variants?: Variants;
}

export default function ProductManagementCard({
  title,
  description,
  features,
  cta,
  icon,
  color,
  bgGradient,
  variants,
}: ProductManagementCardProps) {
  return (
    <motion.div
      className="p-6 rounded-lg border border-gray-800 bg-gradient-to-br from-gray-900 to-black/60 backdrop-blur-sm h-full flex flex-col"
      variants={variants}
      whileHover={{
        scale: 1.03,
        boxShadow: "0 10px 30px -10px rgba(0, 200, 255, 0.2)",
      }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex-grow">
        <div className="text-2xl md:text-3xl font-bold mb-4 bg-gradient-to-br from-cyan-400 to-purple-500 rounded-full h-14 w-14 flex items-center justify-center">
          <span className="transform transition-transform duration-600 hover:rotate-360 text-2xl">
            {icon}
          </span>
        </div>

        <h3 className="text-xl md:text-2xl font-semibold text-white mb-3">
          {title}
        </h3>

        <p className="text-base font-normal text-gray-300 mb-4">
          {description}
        </p>

        <ul className="mb-6 space-y-2">
          {features.map((feature, index) => (
            <li
              key={index}
              className="flex items-start text-gray-300"
            >
              <span className="mr-2 text-cyan-400">
                ✓
              </span>
              {feature}
            </li>
          ))}
        </ul>
      </div>

      <div className="mt-6">
        <button className="text-cyan-400 hover:text-cyan-300 flex items-center group transition-all duration-300 text-sm md:text-base font-medium">
          {cta}
          <svg
            className="ml-1 h-4 w-4 transform group-hover:translate-x-1 transition-transform duration-300"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </motion.div>
  );
}
