'use client';

import { motion } from 'framer-motion';

// Default process details when translations are not available
const defaultProcessDetails: Record<string, { description: string; features: string[] }> = {
  // Product Development processes
  productFeasibility: {
    description: 'Evaluate the technical, market, and financial viability of product concepts before significant investment. Identify potential risks and develop mitigation strategies.',
    features: [
      'Technical feasibility assessment framework',
      'Market opportunity analysis tools',
      'Financial viability calculators',
      'Risk assessment matrices',
      'Regulatory requirement checklists'
    ]
  },
  developmentMedical: {
    description: 'Guide the development of medical products with specialized workflows that ensure compliance with regulatory requirements and clinical standards.',
    features: [
      'Clinical trial planning templates',
      'Regulatory submission checklists',
      'Medical device classification tools',
      'Safety and efficacy documentation',
      'Quality management system integration'
    ]
  },
  rAndD: {
    description: 'Manage research and development activities with structured processes that balance innovation with practical implementation and resource constraints.',
    features: [
      'Innovation pipeline management',
      'Research project tracking',
      'Prototype development workflow',
      'Intellectual property documentation',
      'Technology readiness assessment'
    ]
  },
  productRoadmap: {
    description: 'Create and maintain product roadmaps that align with business strategy, market needs, and development capabilities.',
    features: [
      'Visual roadmap creation tools',
      'Feature prioritization framework',
      'Release planning templates',
      'Stakeholder alignment tools',
      'Roadmap presentation generator'
    ]
  },

  // Market Strategy processes
  productPositioning: {
    description: 'Define how your product will be perceived in the market relative to competitors. Identify unique value propositions and target customer segments.',
    features: [
      'Competitive positioning matrix',
      'Value proposition canvas',
      'Target market segmentation tools',
      'Messaging framework templates',
      'Brand alignment guidelines'
    ]
  },
  competitorAnalysis: {
    description: 'Analyze competitors to identify market opportunities, potential threats, and areas for differentiation.',
    features: [
      'Competitor tracking dashboard',
      'Feature comparison matrices',
      'Pricing strategy analysis',
      'SWOT analysis templates',
      'Market share visualization'
    ]
  },
  licensingStrategy: {
    description: 'Develop strategies for licensing intellectual property, technology, or products to maximize revenue and market reach.',
    features: [
      'Licensing model comparison tools',
      'Royalty calculator',
      'Partner evaluation framework',
      'Contract template library',
      'Revenue projection models'
    ]
  },
  roadmapPlanning: {
    description: 'Create strategic product roadmaps that align with business goals, market opportunities, and resource constraints.',
    features: [
      'Strategic roadmap visualization',
      'Release planning framework',
      'Resource allocation tools',
      'Milestone tracking system',
      'Stakeholder communication templates'
    ]
  },
  yearlyPlanning: {
    description: 'Develop annual profit and loss plans that align with product strategy, market conditions, and business objectives.',
    features: [
      'Annual P&L template',
      'Revenue forecasting tools',
      'Cost allocation framework',
      'Budget vs. actual tracking',
      'Scenario planning models'
    ]
  },

  // Launch Preparation processes
  marketingStrategy: {
    description: 'Develop comprehensive marketing strategies that effectively position your product and reach target audiences.',
    features: [
      'Marketing campaign planner',
      'Channel strategy framework',
      'Content calendar templates',
      'Budget allocation tools',
      'Performance metrics dashboard'
    ]
  },
  launchPreparation: {
    description: 'Prepare for successful product launches with comprehensive planning, coordination, and execution across all departments.',
    features: [
      'Launch readiness checklist',
      'Cross-functional coordination tools',
      'Timeline visualization',
      'Risk management framework',
      'Go/no-go decision criteria'
    ]
  },
  communicationPlan: {
    description: 'Create structured communication plans that ensure all stakeholders receive timely and relevant information throughout the product lifecycle.',
    features: [
      'Stakeholder mapping tools',
      'Communication matrix templates',
      'Message development framework',
      'Channel selection guide',
      'Feedback collection mechanisms'
    ]
  },
  organizationalChart: {
    description: 'Design and maintain organizational structures that support product development, launch, and ongoing management.',
    features: [
      'Role and responsibility matrices',
      'Team structure visualization',
      'Skill gap analysis',
      'Reporting relationship mapping',
      'Resource allocation planning'
    ]
  },
  changeManagement: {
    description: 'Implement structured change management processes to ensure smooth transitions and minimize disruption during product launches or updates.',
    features: [
      'Change impact assessment',
      'Stakeholder readiness evaluation',
      'Training needs analysis',
      'Resistance management strategies',
      'Adoption tracking metrics'
    ]
  },

  // Post Market processes
  postMarketSurveillance: {
    description: 'Monitor product performance after launch to ensure safety, effectiveness, and compliance with regulatory requirements.',
    features: [
      'Adverse event tracking system',
      'Customer feedback analysis',
      'Regulatory compliance monitoring',
      'Quality metrics dashboard',
      'Signal detection algorithms'
    ]
  },
  roadmapReleases: {
    description: 'Plan and execute product releases according to the strategic roadmap, ensuring alignment with market needs and business objectives.',
    features: [
      'Release planning framework',
      'Feature prioritization tools',
      'Development capacity planning',
      'Release readiness checklists',
      'Post-release evaluation templates'
    ]
  }
};

interface ProcessDetailsProps {
  process: string;
  translations?: {
    components?: {
      processDetailsTitle?: string;
      keyFeatures?: string;
      keyElements?: string;
      roadmapDevelopmentProcess?: string;
      roadmapDevelopmentSteps?: string[];
      strategicRoadmapFramework?: string;
      marketAnalysis?: {
        title?: string;
        items?: string[];
      };
      strategicPlanning?: {
        title?: string;
        items?: string[];
      };
      roadmapExecution?: {
        title?: string;
        releaseTimeline?: string;
        phasedImplementation?: string;
        milestoneTracking?: string;
        continuousFeedback?: string;
      };
      businessImpact?: {
        title?: string;
        impacts?: string[];
      };
      tryRoadmapTool?: string;
      roadmapToolDescription?: string;
      roadmapPlanning?: {
        title?: string;
        description?: string;
        elements?: {
          feasibility?: {
            title?: string;
            description?: string;
          };
          positioning?: {
            title?: string;
            description?: string;
          };
          releases?: {
            title?: string;
            description?: string;
          };
          planning?: {
            title?: string;
            description?: string;
          };
        };
      };
      processDetailsContent?: {
        [key: string]: {
          description?: string;
          features?: string[];
        };
      };
    };
  };
  onClose: () => void;
}

export default function ProcessDetails({ process, translations, onClose }: ProcessDetailsProps) {
  return (
    <motion.div
      key={`component-details-${process}`}
      className="mt-8 bg-gray-800/30 border border-cyan-500/30 rounded-lg p-6 shadow-lg relative z-10"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="relative">
        <button
          onClick={onClose}
          className="absolute top-0 right-0 text-gray-400 hover:text-white transition-colors"
          aria-label="Close"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        <h3 className="text-xl font-semibold mb-4 text-cyan-400 pr-8">
          {(translations?.components?.processDetailsTitle || '{process} Process Details').replace('{process}', process)}
        </h3>
      </div>

      {process === 'Roadmap Planning' && (
        <div className="space-y-4">
          <p className="text-gray-300">
            {translations?.components?.roadmapPlanning?.description ||
              'Roadmap Planning is a strategic process that outlines the vision, direction, and progress of product development over time. It aligns stakeholders around key milestones and helps prioritize features based on market needs and business goals.'}
          </p>

          <div className="mt-4">
            <h4 className="text-lg font-medium mb-3 text-white">
              {translations?.components?.keyElements || 'Key Elements:'}
            </h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="bg-gray-800/70 p-3 rounded border border-gray-700">
                <span className="text-cyan-400 font-medium">
                  {translations?.components?.roadmapPlanning?.elements?.feasibility?.title ||
                    'Product Feasibility Assessment'}
                </span>
                <p className="text-sm text-gray-300 mt-1">
                  {translations?.components?.roadmapPlanning?.elements?.feasibility?.description ||
                    'Evaluates technical, market, and financial viability of product concepts before significant investment. Identifies potential risks and mitigation strategies.'}
                </p>
              </div>

              <div className="bg-gray-800/70 p-3 rounded border border-gray-700">
                <span className="text-cyan-400 font-medium">
                  {translations?.components?.roadmapPlanning?.elements?.positioning?.title ||
                    'Product Positioning Strategy'}
                </span>
                <p className="text-sm text-gray-300 mt-1">
                  {translations?.components?.roadmapPlanning?.elements?.positioning?.description ||
                    'Defines how your product will be perceived in the market relative to competitors. Identifies unique value propositions and target customer segments.'}
                </p>
              </div>

              <div className="bg-gray-800/70 p-3 rounded border border-gray-700">
                <span className="text-cyan-400 font-medium">
                  {translations?.components?.roadmapPlanning?.elements?.releases?.title ||
                    'Release Planning'}
                </span>
                <p className="text-sm text-gray-300 mt-1">
                  {translations?.components?.roadmapPlanning?.elements?.releases?.description ||
                    'Structures product development into strategic releases with clear objectives and timelines. Balances feature delivery with market windows and resource constraints.'}
                </p>
              </div>

              <div className="bg-gray-800/70 p-3 rounded border border-gray-700">
                <span className="text-cyan-400 font-medium">
                  {translations?.components?.roadmapPlanning?.elements?.planning?.title ||
                    'Financial Planning'}
                </span>
                <p className="text-sm text-gray-300 mt-1">
                  {translations?.components?.roadmapPlanning?.elements?.planning?.description ||
                    'Projects revenue, costs, and profitability throughout the product lifecycle. Establishes key financial metrics and targets for measuring success.'}
                </p>
              </div>
            </div>
          </div>

          <div className="mt-4">
            <h4 className="text-lg font-medium mb-3 text-white">
              {translations?.components?.roadmapDevelopmentProcess || 'Roadmap Development Process:'}
            </h4>
            <div className="p-4 bg-black/50 rounded-lg border border-gray-800">
              <ol className="space-y-2 text-gray-300">
                {(translations?.components?.roadmapDevelopmentSteps || [
                  'Gather market intelligence and customer requirements',
                  'Assess technical feasibility and resource requirements',
                  'Prioritize features based on business value and strategic alignment',
                  'Define release timelines and key milestones',
                  'Secure stakeholder alignment and commitment'
                ]).map((step, index) => (
                  <li key={`step-${index}`} className="flex items-start">
                    <span className="text-cyan-400 mr-2">{index + 1}.</span>
                    <span>{step}</span>
                  </li>
                ))}
              </ol>
            </div>
          </div>

          {/* Visual Diagram of Roadmap Planning Process */}
          <div className="mt-8 bg-gray-800/50 rounded-lg p-6 border border-gray-700">
            <h4 className="text-lg font-medium mb-4 text-white">
              {translations?.components?.strategicRoadmapFramework || 'Strategic Roadmap Planning Framework'}
            </h4>

            <div className="relative">
              {/* Flow Diagram */}
              <div className="flex flex-col md:flex-row items-center justify-between gap-4 relative">
                {/* Step 1: Market Analysis */}
                <div className="bg-gray-900/80 p-4 rounded-lg border border-purple-500/30 w-full md:w-1/4 z-10">
                  <div className="text-cyan-400 mb-2 font-medium text-center">
                    {translations?.components?.marketAnalysis?.title || 'Market Analysis'}
                  </div>
                  <div className="flex flex-col gap-2">
                    {(translations?.components?.marketAnalysis?.items || [
                      'Customer Needs',
                      'Competitive Landscape',
                      'Industry Trends',
                      'Regulatory Requirements'
                    ]).map((item, index) => (
                      <div key={index} className="bg-gray-800 p-2 rounded border border-gray-700 text-sm text-gray-300 text-center">
                        {item}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Arrow */}
                <div className="hidden md:block text-purple-500 z-10">
                  <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </div>
                <div className="block md:hidden text-purple-500 z-10 -my-2">
                  <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                  </svg>
                </div>

                {/* Step 2: Strategic Planning */}
                <div className="bg-gray-900/80 p-4 rounded-lg border border-purple-500/30 w-full md:w-1/4 z-10">
                  <div className="text-cyan-400 mb-2 font-medium text-center">
                    {translations?.components?.strategicPlanning?.title || 'Strategic Planning'}
                  </div>
                  <div className="flex flex-col gap-2">
                    {(translations?.components?.strategicPlanning?.items || [
                      'Vision & Goals',
                      'Feature Prioritization',
                      'Resource Allocation'
                    ]).map((item, index) => (
                      <div key={index} className="bg-gray-800 p-2 rounded border border-gray-700 text-sm text-gray-300">
                        <span className="font-medium">{item}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Arrow */}
                <div className="hidden md:block text-purple-500 z-10">
                  <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </div>
                <div className="block md:hidden text-purple-500 z-10 -my-2">
                  <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                  </svg>
                </div>

                {/* Step 3: Roadmap Execution */}
                <div className="bg-gradient-to-br from-purple-900/50 to-cyan-900/50 p-4 rounded-lg border border-cyan-500/50 w-full md:w-1/4 z-10 shadow-lg">
                  <div className="text-cyan-400 mb-2 font-medium text-center">
                    {translations?.components?.roadmapExecution?.title || 'Roadmap Execution'}
                  </div>
                  <div className="bg-gray-800/80 p-3 rounded border border-cyan-600/30 text-center">
                    <div className="text-white font-medium mb-1">
                      {translations?.components?.roadmapExecution?.releaseTimeline || 'Release Timeline'}
                    </div>
                    <div className="text-xs text-gray-300">
                      {translations?.components?.roadmapExecution?.phasedImplementation || 'Phased implementation'}
                    </div>
                    <div className="mt-2 text-xs text-cyan-400">
                      {translations?.components?.roadmapExecution?.milestoneTracking || 'Milestone tracking'}
                    </div>
                  </div>
                  <div className="mt-3 text-center text-xs text-gray-400">
                    {translations?.components?.roadmapExecution?.continuousFeedback || 'Continuous feedback and adaptation'}
                  </div>
                </div>
              </div>

              {/* Background Connection Line */}
              <div className="absolute top-1/2 left-0 w-full h-1 bg-gradient-to-r from-purple-600/20 via-cyan-500/20 to-purple-600/20 -translate-y-1/2 hidden md:block"></div>
            </div>
          </div>

          {/* Business Impact Section */}
          <div className="mt-6">
            <h4 className="text-lg font-medium mb-3 text-white">
              {translations?.components?.businessImpact?.title || 'Business Impact'}
            </h4>
            <div className="p-4 bg-black/50 rounded-lg border border-gray-800">
              <ul className="space-y-2 text-gray-300">
                {(translations?.components?.businessImpact?.impacts || [
                  'Reduces time-to-market by 30-40% through strategic planning and prioritization',
                  'Improves resource utilization by 25% through better alignment with strategic goals',
                  'Enhances stakeholder alignment and reduces costly mid-development pivots'
                ]).map((impact, index) => (
                  <li key={`impact-${index}`} className="flex items-start">
                    <span className="text-cyan-400 mr-2">→</span>
                    <span>{impact}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Try Roadmap Planning Tool Button */}
          <div className="mt-8 text-center">
            <a
              href="#roadmap-builder"
              className="inline-flex items-center bg-gradient-to-r from-purple-600 to-cyan-500 text-white font-medium py-3 px-6 rounded-lg hover:from-purple-700 hover:to-cyan-600 transition-colors"
            >
              <span>{translations?.components?.tryRoadmapTool || 'Try Our Roadmap Planning Tool'}</span>
              <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </a>
            <p className="text-gray-400 text-sm mt-2">
              {translations?.components?.roadmapToolDescription ||
                'Experience how our roadmap planning tools can streamline your product development process'}
            </p>
          </div>
        </div>
      )}

      {process !== 'Roadmap Planning' && (
        <div className="space-y-4">
          {/* Get the process key from the selected component */}
          {(() => {
            // Convert the selected component to a camelCase key
            const processKey = process
              ?.toLowerCase()
              .replace(/\s+/g, ' ')
              .split(' ')
              .map((word, index) =>
                index === 0
                  ? word.toLowerCase()
                  : word.charAt(0).toUpperCase() + word.slice(1)
              )
              .join('')
              .replace(/[^a-zA-Z0-9]/g, '');

            // Get the process details from translations, default details, or use fallback
            const translatedDetails = translations?.components?.processDetailsContent?.[processKey];
            const defaultDetails = defaultProcessDetails[processKey];

            // Determine which description and features to use
            const description = translatedDetails?.description || defaultDetails?.description ||
              `The ${process} component provides specialized functionality for this part of the product management process.`;

            const features = translatedDetails?.features || defaultDetails?.features || [
              `Specialized tools for ${process}`,
              `Workflow templates for ${process} management`,
              `Analytics and reporting for ${process} tracking`,
              `Integration with other product management processes`
            ];

            return (
              <>
                <p className="text-gray-300">{description}</p>
                <div className="mt-4">
                  <h4 className="text-lg font-medium mb-3 text-white">
                    {translations?.components?.keyFeatures || 'Key Features:'}
                  </h4>
                  <div className="p-4 bg-black/50 rounded-lg border border-gray-800">
                    <ul className="space-y-2 text-gray-300">
                      {features.map((feature, index) => (
                        <li key={`feature-${index}`} className="flex items-start">
                          <span className="text-cyan-400 mr-2">→</span>
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </>
            );
          })()}
        </div>
      )}
    </motion.div>
  );
}
